/**
 * Filin Base Trace - Основная точка входа для трассировки
 * 
 * Экспортирует все компоненты системы трассировки для использования
 * в других модулях (tabby-agent, tabby-chat-panel, vscode, intellij)
 * 
 * @example
 * ```typescript
 * import { TraceUtilsBase, DataMasker, TraceEventTypes } from 'filin-base/trace';
 * 
 * const traceId = TraceUtilsBase.generateTraceId();
 * const config = ConfigLoaderBase.loadConfig();
 * const masker = new DataMasker(config);
 * ```
 */

// Базовые типы и константы
export * from './TraceTypes';

// Утилиты маскировки данных
export * from './DataMasker';

// Базовый загрузчик конфигурации (только для Node.js проектов) - УДАЛЕН
// export * from './ConfigLoaderBase';

// Базовые утилиты трассировки
export * from './TraceUtilsBase';

// Утилиты конфигурации
export * from './TraceConfig';
export { TraceConfigManager } from './TraceConfig';

// Детектор зависаний
export * from './HangDetector';

// Удобные реэкспорты для быстрого доступа
export { TraceUtilsBase as TraceUtils } from './TraceUtilsBase';
export { generateTraceId } from './TraceUtilsBase';
// Экспорт генератора идентификаторов для браузерных адаптеров
export { IdGenerator, setIdGenerator, getIdGenerator, FallbackIdGenerator, initializeFallbackGenerator } from './IdGenerator';

// Экспорт функции проверки включения трассировки
import { TraceConfigManager } from './TraceConfig';
export const isTracingEnabled = TraceConfigManager.isTracingEnabled;

// Экспорт миграционных утилит для обратной совместимости
export * from './MigrationUtils';

// Экспорт канонических имен полей и утилит
export * from './CanonicalFields';
export * from './CanonicalEventHelper';