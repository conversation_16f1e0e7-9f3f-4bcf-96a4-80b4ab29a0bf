/**
 * Filin Tracer - Утилиты трассировки для Chat Panel
 * 
 * Принцип: Расширение базовых утилит для чат-специфичных операций
 * Интеграция: Использует filin-base для общей функциональности с W3C Trace Context
 * Логирование: JSON Lines формат с OpenTelemetry семантикой
 */

import {
  TraceConfig,
  DataMasker,
  TraceEventTypes,
  CanonicalFields,
  type TraceEvent,
  FILIN_SCHEMA_ID
} from 'filin-base/browser';
import { TraceUtilsBase } from './TraceUtilsBrowser';
import { TraceConfigBrowser } from './TraceConfigBrowser';
import {
  W3CTraceContext,
  W3CTraceState,
  W3CBaggage,
  type SpanContext
} from 'filin-base/browser';
import { BrowserAdapter } from '../adapters/BrowserAdapter';
import { BrowserTransportManager, LocalStorageTransport, FetchTransport, ConsoleTransport } from '../transports';
// import { EventValidator } from 'filin-base/schema';

/**
 * Чат-специфичные типы операций (используем канонические типы из filin-base)
 */
export const ChatOperationType = {
  // Основные чат события
  CHAT_MESSAGE_SENT: TraceEventTypes.CHAT_MESSAGE_SENT,
  CHAT_REQUEST: TraceEventTypes.CHAT_REQUEST,
  CHAT_RESPONSE: TraceEventTypes.CHAT_RESPONSE,

  // Стриминг события
  CHAT_STREAM_START: TraceEventTypes.CHAT_STREAM_START,
  CHAT_STREAM_CHUNK: TraceEventTypes.CHAT_STREAM_CHUNK,
  CHAT_STREAM_END: TraceEventTypes.CHAT_STREAM_END,

  // Спан события
  SPAN_START: TraceEventTypes.SPAN_START,
  SPAN_END: TraceEventTypes.SPAN_END,

  // Медленные операции
  SLOW_OPERATION: TraceEventTypes.SLOW_OPERATION
} as const;

export type ChatOperationTypeValue = typeof ChatOperationType[keyof typeof ChatOperationType];

/**
 * Интерфейс для чат-сообщения
 */
export interface ChatMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: number;
  traceId?: string;
  spanId?: string;
  parentSpanId?: string;
}

/**
 * Интерфейс для чат-команды трассировки
 */
export interface ChatTraceCommand {
  type: ChatOperationTypeValue;
  content: string;
  context?: any;
  traceId?: string;
  spanId?: string;
  parentSpanId?: string;
}

/**
 * Интерфейс для SSE стриминга
 */
export interface SSEStreamContext {
  streamId: string;
  traceId: string;
  spanId: string;
  parentSpanId?: string;
  startTime: number;
  firstTokenTime?: number;
  lastByteTime?: number;
  chunkCount: number;
  totalBytes: number;
  totalTokens?: number;
}

/**
 * Интерфейс для WebSocket трассировки
 */
export interface WebSocketTraceContext {
  connectionId: string;
  traceId: string;
  spanId: string;
  parentSpanId?: string;
  url: string;
  protocols?: string[];
  startTime: number;
}

/**
 * Утилиты трассировки для Chat Panel
 * Расширяет базовые утилиты чат-специфичными методами с W3C Trace Context
 */
export class ChatTraceUtils extends TraceUtilsBase {
  protected config: TraceConfig;
  protected dataMasker: DataMasker;
  // protected eventValidator: EventValidator;
  private sessionId: string;
  private sessionSeq: number = 0;
  private browserAdapter: BrowserAdapter;
  private transportManager: BrowserTransportManager;

  constructor(config: TraceConfig) {
    super();
    this.config = config;
    this.dataMasker = new DataMasker(config);
    // this.eventValidator = new EventValidator();
    this.sessionId = this.generateSessionId();

    // Initialize browser adapter and transport manager
    this.browserAdapter = new BrowserAdapter({
      telemetryEndpoint: config.otlp?.endpoint?.replace(':4317', '/api/telemetry'),
      enableLocalStorage: true
    });

    // Initialize transport manager with multiple backends
    this.transportManager = new BrowserTransportManager({
      console: { enabled: true, format: 'json' },
      localStorage: { enabled: true },
      fetch: config.otlp?.endpoint ? {
        enabled: true,
        endpoint: config.otlp.endpoint.replace(':4317', '/api/telemetry'),
        headers: config.otlp.headers || {},
        batchSize: 50,
        batchTimeout: 30000
      } : undefined
    });
  }

  /**
   * Получает конфигурацию трассировки
   */
  public getConfig(): TraceConfig {
    return this.config;
  }

  /**
   * Генерирует session_id для lifetime процесса
   */
  private generateSessionId(): string {
    return `chat-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Получает следующий sequence number для сессии
   */
  private getNextSessionSeq(): number {
    return ++this.sessionSeq;
  }

  /**
   * Создает базовое событие трассировки в JSON Lines формате
   */
  public createBaseEvent(
    eventType: string,
    traceId: string,
    spanId: string,
    parentSpanId?: string
  ): TraceEvent {
    // Получаем service info из конфигурации
    const serviceInfo = TraceConfigBrowser.getServiceInfo();

    // Используем статический метод из базового класса
    const baseEvent = TraceUtilsBase.createBaseEventFull(
      eventType as any,
      traceId,
      spanId,
      parentSpanId,
      serviceInfo,
      'chat-handler',
      'INFO'
    );

    // Добавляем специфичные для чата поля
    baseEvent[CanonicalFields.SESSION_ID] = this.sessionId;
    baseEvent[CanonicalFields.IDE_SESSION_SEQ] = this.getNextSessionSeq();
    baseEvent[CanonicalFields.DATA_CLASSIFICATION] = 'internal';
    baseEvent[CanonicalFields.EVENT_ID] = generateEventId(); // Перезаписываем для UUIDv7

    return baseEvent;
  }

  /**
   * Логирует событие трассировки в JSON Lines формате
   */
  public logEvent(event: TraceEvent): void {
    if (!this.config.enabled) return;

    try {
      // TODO: Add event validation when EventValidator is available
      // const validationResult = this.eventValidator.validate(event);
      // if (!validationResult.valid) {
      //   console.warn('[FilinTracer] Event validation failed:', validationResult.errors);
      //   return;
      // }

      // Маскируем чувствительные данные (пока используем оригинальное событие)
      const maskedEvent = event;

      // Отправляем через все настроенные транспорты
      this.transportManager.send(maskedEvent);

      // Browser adapter используется только для дополнительной функциональности (localStorage)
      // Логирование в консоль уже происходит через TransportManager
    } catch (error) {
      console.error('[FilinTracer] Failed to log event:', error);
    }
  }

  /**
   * Создает W3C Trace Context
   */
  createTraceContext(traceId: string, spanId: string, sampled: boolean = true): SpanContext {
    const traceparent = W3CTraceContext.createTraceparent(traceId, spanId, sampled);
    const tracestate = W3CTraceState.createFilinTracestate(
      this.sessionId.substring(0, 8),
      'unknown',
      'chat-panel'
    );
    const baggage = W3CBaggage.createFilinBaggage(
      'chat-panel',
      'unknown',
      this.sessionId.substring(0, 8)
    );

    return {
      traceId,
      spanId,
      traceparent,
      tracestate,
      baggage
    };
  }

  /**
   * Логирует отправку чат-сообщения
   * @param message чат-сообщение
   * @param traceId идентификатор трассировки (генерируется автоматически если не указан)
   * @param parentSpanId родительский span ID
   * @return объект с traceId и spanId
   */
  chatMessageSent(
    message: ChatMessage,
    traceId: string = generateTraceId(),
    parentSpanId?: string
  ): { traceId: string; spanId: string } {
    if (!this.config.enabled) return { traceId, spanId: generateSpanId() };

    const spanId = generateSpanId();
    const maskedContent = this.dataMasker.maskSensitiveData(message.content);

    const event = this.createBaseEvent(
      ChatOperationType.CHAT_MESSAGE_SENT,
      traceId,
      spanId,
      parentSpanId
    );

    // Добавляем чат-специфичные поля
    Object.assign(event, {
      [CanonicalFields.OPERATION]: 'chat_message',
      [CanonicalFields.MESSAGE_ID]: message.id,
      [CanonicalFields.MESSAGE_ROLE]: message.role,
      [CanonicalFields.MESSAGE_CONTENT_LENGTH]: message.content.length,
      [CanonicalFields.MESSAGE_HASH]: `sha256:${maskedContent.maskedData.substring(0, 8)}...`
    });

    this.logEvent(event);
    return { traceId, spanId };
  }

  /**
   * Логирует получение ответа от чата
   * @param message ответное сообщение
   * @param traceId идентификатор трассировки
   * @param spanId идентификатор спана
   * @param duration длительность обработки в миллисекундах
   * @param parentSpanId родительский span ID
   */
  chatResponseReceived(
    message: ChatMessage,
    traceId: string,
    spanId: string,
    duration: number,
    parentSpanId?: string
  ): void {
    if (!this.config.enabled) return;

    const maskedContent = this.dataMasker.maskSensitiveData(message.content);
    const event = this.createBaseEvent(
      ChatOperationType.CHAT_RESPONSE,
      traceId,
      spanId,
      parentSpanId
    );

    Object.assign(event, {
      [CanonicalFields.OPERATION]: 'chat_response',
      [CanonicalFields.DURATION_MS]: duration,
      [CanonicalFields.MESSAGE_ID]: message.id,
      [CanonicalFields.MESSAGE_ROLE]: message.role,
      [CanonicalFields.MESSAGE_CONTENT_LENGTH]: message.content.length,
      [CanonicalFields.MESSAGE_HASH]: `sha256:${maskedContent.maskedData.substring(0, 8)}...`,
      [CanonicalFields.STOP_REASON]: 'complete'
    });

    this.logEvent(event);
  }

  /**
   * Начинает трассировку SSE стрима
   * @param streamId идентификатор стрима
   * @param traceId идентификатор трассировки
   * @param parentSpanId родительский span ID
   * @param modelName имя модели
   * @return контекст стрима
   */
  startSSEStream(
    streamId: string,
    traceId: string,
    parentSpanId?: string,
    modelName?: string
  ): SSEStreamContext {
    const spanId = generateSpanId();
    const startTime = Date.now();

    if (this.config.enabled) {
      const event = this.createBaseEvent(
        ChatOperationType.CHAT_STREAM_START,
        traceId,
        spanId,
        parentSpanId
      );

      Object.assign(event, {
        [CanonicalFields.OPERATION]: 'chat_stream',
        [CanonicalFields.STREAM_ID]: streamId,
        [CanonicalFields.GEN_AI_REQUEST_MODEL]: modelName || 'unknown'
      });

      this.logEvent(event);
    }

    return {
      streamId,
      traceId,
      spanId,
      parentSpanId,
      startTime,
      chunkCount: 0,
      totalBytes: 0,
      totalTokens: 0
    };
  }

  /**
   * Логирует чанк SSE стрима
   * @param context контекст стрима
   * @param chunkData данные чанка
   * @param isFirstToken является ли первым токеном
   */
  logSSEChunk(
    context: SSEStreamContext,
    chunkData: { content?: string; tokens?: number },
    isFirstToken: boolean = false
  ): void {
    if (!this.config.enabled) return;

    context.chunkCount++;
    if (chunkData.content) {
      context.totalBytes += new TextEncoder().encode(chunkData.content).length;
    }
    if (chunkData.tokens) {
      context.totalTokens = (context.totalTokens || 0) + chunkData.tokens;
    }

    const now = Date.now();
    if (isFirstToken && !context.firstTokenTime) {
      context.firstTokenTime = now;
    }
    context.lastByteTime = now;

    // Логируем каждый N-й чанк (используем значение из конфигурации)
    const config = TraceConfigBrowser.loadDefaultConfig();
    const logEveryN = config.streaming?.logEveryNChunks || 10;
    if (context.chunkCount === 1 || context.chunkCount % logEveryN === 0) {
      const event = this.createBaseEvent(
        ChatOperationType.CHAT_STREAM_CHUNK,
        context.traceId,
        context.spanId,
        context.parentSpanId
      );

      Object.assign(event, {
        [CanonicalFields.OPERATION]: 'chat_stream_chunk',
        [CanonicalFields.STREAM_ID]: context.streamId,
        [CanonicalFields.STREAM_SEQ]: context.chunkCount,
        [CanonicalFields.CHUNK_INDEX]: context.chunkCount
      });

      if (isFirstToken && context.firstTokenTime) {
        event[CanonicalFields.TIME_TO_FIRST_TOKEN] = context.firstTokenTime - context.startTime;
      }

      this.logEvent(event);
    }
  }

  /**
   * Завершает трассировку SSE стрима
   * @param context контекст стрима
   * @param stopReason причина остановки
   */
  endSSEStream(
    context: SSEStreamContext,
    stopReason: 'complete' | 'canceled_by_user' | 'deadline_exceeded' | 'error' = 'complete'
  ): void {
    if (!this.config.enabled) return;

    const endTime = Date.now();
    const event = this.createBaseEvent(
      ChatOperationType.CHAT_STREAM_END,
      context.traceId,
      context.spanId,
      context.parentSpanId
    );

    Object.assign(event, {
      [CanonicalFields.OPERATION]: 'chat_stream_end',
      [CanonicalFields.STREAM_ID]: context.streamId,
      [CanonicalFields.STREAM_CHUNKS_TOTAL]: context.chunkCount,
      [CanonicalFields.BYTES_TOTAL]: context.totalBytes,
      [CanonicalFields.GEN_AI_USAGE_OUTPUT_TOKENS]: context.totalTokens || 0,
      stop_reason: stopReason
    });

    if (context.firstTokenTime) {
      event[CanonicalFields.TIME_TO_FIRST_TOKEN] = context.firstTokenTime - context.startTime;
    }
    if (context.lastByteTime) {
      event[CanonicalFields.TIME_TO_LAST_BYTE] = context.lastByteTime - context.startTime;
    }

    this.logEvent(event);
  }

  /**
   * Начинает трассировку WebSocket соединения
   * @param connectionId идентификатор соединения
   * @param url URL WebSocket
   * @param protocols протоколы
   * @param traceId идентификатор трассировки
   * @param parentSpanId родительский span ID
   * @return контекст WebSocket
   */
  startWebSocketConnection(
    connectionId: string,
    url: string,
    protocols?: string[],
    traceId: string = generateTraceId(),
    parentSpanId?: string
  ): WebSocketTraceContext {
    const spanId = generateSpanId();
    const startTime = Date.now();

    if (this.config.enabled) {
      const event = this.createBaseEvent(
        TraceEventTypes.SPAN_START,
        traceId,
        spanId,
        parentSpanId
      );

      Object.assign(event, {
        [CanonicalFields.OPERATION]: 'websocket_connection',
        [CanonicalFields.WEBSOCKET_CONNECTION_ID]: connectionId,
        [CanonicalFields.WEBSOCKET_URL]: url,
        [CanonicalFields.WEBSOCKET_PROTOCOLS]: protocols?.join(',') || ''
      });

      this.logEvent(event);
    }

    return {
      connectionId,
      traceId,
      spanId,
      parentSpanId,
      url,
      protocols,
      startTime
    };
  }

  /**
   * Логирует сообщение WebSocket
   * @param context контекст WebSocket
   * @param direction направление сообщения
   * @param messageType тип сообщения
   * @param size размер сообщения
   */
  logWebSocketMessage(
    context: WebSocketTraceContext,
    direction: 'sent' | 'received',
    messageType: 'text' | 'binary' | 'ping' | 'pong' | 'close',
    size: number
  ): void {
    if (!this.config.enabled) return;

    const event = this.createBaseEvent(
      'WEBSOCKET_MESSAGE',
      context.traceId,
      generateSpanId(),
      context.spanId
    );

    Object.assign(event, {
      [CanonicalFields.OPERATION]: 'websocket_message',
      [CanonicalFields.WEBSOCKET_CONNECTION_ID]: context.connectionId,
      [CanonicalFields.WEBSOCKET_DIRECTION]: direction,
      [CanonicalFields.WEBSOCKET_MESSAGE_TYPE]: messageType,
      [CanonicalFields.WEBSOCKET_MESSAGE_SIZE]: size
    });

    this.logEvent(event);
  }

  /**
   * Завершает трассировку WebSocket соединения
   * @param context контекст WebSocket
   * @param closeCode код закрытия
   * @param closeReason причина закрытия
   */
  endWebSocketConnection(
    context: WebSocketTraceContext,
    closeCode?: number,
    closeReason?: string
  ): void {
    if (!this.config.enabled) return;

    const endTime = Date.now();
    const event = this.createBaseEvent(
      TraceEventTypes.SPAN_END,
      context.traceId,
      context.spanId,
      context.parentSpanId
    );

    Object.assign(event, {
      [CanonicalFields.OPERATION]: 'websocket_connection',
      [CanonicalFields.WEBSOCKET_CONNECTION_ID]: context.connectionId,
      [CanonicalFields.WEBSOCKET_CLOSE_CODE]: closeCode,
      [CanonicalFields.WEBSOCKET_CLOSE_REASON]: closeReason || '',
      [CanonicalFields.DURATION_MS]: endTime - context.startTime,
      [CanonicalFields.STOP_REASON]: closeCode === 1000 ? 'complete' : 'error'
    });

    this.logEvent(event);
  }

  /**
   * Логирует выполнение чат-команды
   * @param command чат-команда
   * @param traceId идентификатор трассировки (генерируется автоматически если не указан)
   * @param parentSpanId родительский span ID
   * @return объект с traceId и spanId
   */
  chatCommand(
    command: ChatTraceCommand,
    traceId: string = generateTraceId(),
    parentSpanId?: string
  ): { traceId: string; spanId: string } {
    if (!this.config.enabled) return { traceId, spanId: generateSpanId() };

    const spanId = generateSpanId();
    const maskedContent = this.dataMasker.maskSensitiveData(command.content);
    const event = this.createBaseEvent(
      ChatOperationType.CHAT_REQUEST,
      traceId,
      spanId,
      parentSpanId
    );

    Object.assign(event, {
      [CanonicalFields.OPERATION]: 'chat_command',
      [CanonicalFields.CHAT_COMMAND_TYPE]: command.type,
      [CanonicalFields.MESSAGE_CONTENT_LENGTH]: command.content.length,
      [CanonicalFields.MESSAGE_HASH]: `sha256:${maskedContent.maskedData.substring(0, 8)}...`,
      [CanonicalFields.CONTEXT_HAS_CONTENT]: !!command.context
    });

    this.logEvent(event);
    return { traceId, spanId };
  }

  /**
   * Создает спан для операции
   * @param operation название операции
   * @param traceId идентификатор трассировки
   * @param parentSpanId родительский span ID
   * @return объект с traceId и spanId
   */
  startSpan(
    operation: string,
    traceId: string = generateTraceId(),
    parentSpanId?: string
  ): { traceId: string; spanId: string } {
    const spanId = generateSpanId();

    if (this.config.enabled) {
      const event = this.createBaseEvent(
        ChatOperationType.SPAN_START,
        traceId,
        spanId,
        parentSpanId
      );

      Object.assign(event, {
        [CanonicalFields.OPERATION]: operation
      });

      this.logEvent(event);
    }

    return { traceId, spanId };
  }

  /**
   * Завершает спан
   * @param traceId идентификатор трассировки
   * @param spanId идентификатор спана
   * @param parentSpanId родительский span ID
   * @param duration длительность в миллисекундах
   * @param stopReason причина завершения
   */
  endSpan(
    traceId: string,
    spanId: string,
    parentSpanId: string | undefined,
    duration: number,
    stopReason: 'complete' | 'canceled_by_user' | 'deadline_exceeded' | 'error' = 'complete'
  ): void {
    if (!this.config.enabled) return;

    const event = this.createBaseEvent(
      ChatOperationType.SPAN_END,
      traceId,
      spanId,
      parentSpanId
    );

    Object.assign(event, {
      [CanonicalFields.DURATION_MS]: duration,
      [CanonicalFields.STOP_REASON]: stopReason
    });

    this.logEvent(event);
  }

  /**
   * Логирует Smart Apply операцию
   * @param changes изменения для применения
   * @param success успешность применения
   * @param duration длительность операции в миллисекундах
   * @param traceId идентификатор трассировки
   * @param parentSpanId родительский span ID
   * @return объект с traceId и spanId
   */
  smartApply(
    changes: any,
    success: boolean,
    duration: number,
    traceId: string = generateTraceId(),
    parentSpanId?: string
  ): { traceId: string; spanId: string } {
    if (!this.config.enabled) return { traceId, spanId: generateSpanId() };

    const spanId = generateSpanId();
    const event = this.createBaseEvent(
      ChatOperationType.SPAN_START,
      traceId,
      spanId,
      parentSpanId
    );

    Object.assign(event, {
      [CanonicalFields.OPERATION]: 'smart_apply',
      [CanonicalFields.SMART_APPLY_CHANGES_COUNT]: Array.isArray(changes) ? changes.length : 1,
      [CanonicalFields.SMART_APPLY_HAS_CHANGES]: !!changes,
      [CanonicalFields.SMART_APPLY_SUCCESS]: success,
      [CanonicalFields.DURATION_MS]: duration,
      [CanonicalFields.STOP_REASON]: success ? 'complete' : 'error'
    });

    this.logEvent(event);
    return { traceId, spanId };
  }

  /**
   * Логирует Generate Commit Message операцию
   * @param changes изменения для коммита
   * @param generatedMessage сгенерированное сообщение коммита
   * @param success успешность генерации
   * @param duration длительность операции в миллисекундах
   * @param traceId идентификатор трассировки
   * @param parentSpanId родительский span ID
   * @return объект с traceId и spanId
   */
  generateCommitMessage(
    changes: any,
    generatedMessage: string,
    success: boolean,
    duration: number,
    traceId: string = generateTraceId(),
    parentSpanId?: string
  ): { traceId: string; spanId: string } {
    if (!this.config.enabled) return { traceId, spanId: generateSpanId() };

    const spanId = generateSpanId();
    const maskedMessage = this.dataMasker.maskSensitiveData(generatedMessage);

    const event = this.createBaseEvent(
      ChatOperationType.SPAN_START,
      traceId,
      spanId,
      parentSpanId
    );

    Object.assign(event, {
      [CanonicalFields.OPERATION]: 'generate_commit_message',
      [CanonicalFields.COMMIT_CHANGES_COUNT]: Array.isArray(changes) ? changes.length : 1,
      [CanonicalFields.COMMIT_HAS_CHANGES]: !!changes,
      [CanonicalFields.COMMIT_MESSAGE_LENGTH]: generatedMessage.length,
      [CanonicalFields.COMMIT_MESSAGE_HASH]: `sha256:${maskedMessage.maskedData.substring(0, 8)}...`,
      [CanonicalFields.COMMIT_SUCCESS]: success,
      [CanonicalFields.DURATION_MS]: duration,
      [CanonicalFields.STOP_REASON]: success ? 'complete' : 'error'
    });

    this.logEvent(event);
    return { traceId, spanId };
  }

  /**
   * Выполняет чат-операцию с автоматической трассировкой
   * @param operationType тип операции
   * @param operation функция операции
   * @param context контекст операции (опционально)
   * @param parentSpanId родительский span ID
   * @return результат операции
   */
  async traceChatOperation<T>(
    operationType: string,
    operation: (traceId: string, spanId: string) => Promise<T>,
    context?: any,
    parentSpanId?: string
  ): Promise<T> {
    const traceId = generateTraceId();
    const { spanId } = this.startSpan(operationType, traceId, parentSpanId);
    const startTime = Date.now();

    try {
      // Выполняем операцию
      const result = await operation(traceId, spanId);

      // Логируем успешное завершение
      const duration = Date.now() - startTime;
      this.endSpan(traceId, spanId, parentSpanId, duration, 'complete');

      return result;
    } catch (error) {
      // Логируем ошибку
      const duration = Date.now() - startTime;
      this.endSpan(traceId, spanId, parentSpanId, duration, 'error');

      throw error;
    }
  }

  /**
   * Создает обертку для SSE fetch с трассировкой
   * @param url URL для SSE
   * @param options опции fetch
   * @param traceId идентификатор трассировки
   * @param parentSpanId родительский span ID
   * @param idempotencyKey ключ идемпотентности для запроса
   * @return Promise с Response и контекстом трассировки
   */
  async createTracedSSEFetch(
    url: string,
    options: RequestInit = {},
    traceId: string = generateTraceId(),
    parentSpanId?: string,
    idempotencyKey?: string
  ): Promise<{ response: Response; context: SSEStreamContext }> {
    // Инжектируем W3C Trace Context в заголовки
    const spanId = generateSpanId();
    const traceContext = this.createTraceContext(traceId, spanId);

    const headers = new Headers(options.headers);
    headers.set('traceparent', traceContext.traceparent);
    if (traceContext.tracestate) {
      headers.set('tracestate', traceContext.tracestate);
    }
    if (traceContext.baggage) {
      headers.set('baggage', traceContext.baggage);
    }

    // Добавляем Idempotency-Key для обеспечения идемпотентности запросов
    const requestIdempotencyKey = idempotencyKey || `sse-req-${generateEventId()}`;
    headers.set('Idempotency-Key', requestIdempotencyKey);

    // Логируем HTTP_REQUEST событие
    const httpRequestEvent = this.createBaseEvent(
      TraceEventTypes.HTTP_REQUEST,
      traceId,
      spanId,
      parentSpanId
    );
    Object.assign(httpRequestEvent, {
      [CanonicalFields.HTTP_REQUEST_METHOD]: 'GET',
      [CanonicalFields.URL_PATH]: url,
      [CanonicalFields.REQUEST_IDEMPOTENCY_KEY]: requestIdempotencyKey
    });
    this.logEvent(httpRequestEvent);

    const response = await fetch(url, {
      ...options,
      headers
    });

    // Логируем HTTP_RESPONSE событие
    const httpResponseEvent = this.createBaseEvent(
      TraceEventTypes.HTTP_RESPONSE,
      traceId,
      spanId,
      parentSpanId
    );
    Object.assign(httpResponseEvent, {
      [CanonicalFields.HTTP_REQUEST_METHOD]: 'GET',
      [CanonicalFields.URL_PATH]: url,
      [CanonicalFields.HTTP_RESPONSE_STATUS_CODE]: response.status,
      [CanonicalFields.REQUEST_IDEMPOTENCY_KEY]: requestIdempotencyKey
    });
    this.logEvent(httpResponseEvent);

    const streamId = `sse-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const context = this.startSSEStream(streamId, traceId, parentSpanId);

    return { response, context };
  }

  /**
   * Создает обертку для WebSocket с трассировкой
   * @param url URL для WebSocket
   * @param protocols протоколы
   * @param traceId идентификатор трассировки
   * @param parentSpanId родительский span ID
   * @return объект с WebSocket и контекстом трассировки
   */
  createTracedWebSocket(
    url: string,
    protocols?: string[],
    traceId: string = generateTraceId(),
    parentSpanId?: string
  ): { ws: WebSocket; context: WebSocketTraceContext } {
    // Инжектируем контекст в URL параметры для handshake
    const wsUrl = new URL(url);
    const spanId = generateSpanId();
    const traceContext = this.createTraceContext(traceId, spanId);

    wsUrl.searchParams.set('traceparent', traceContext.traceparent);
    if (traceContext.baggage) {
      wsUrl.searchParams.set('baggage', traceContext.baggage);
    }

    const ws = new WebSocket(wsUrl.toString(), protocols);
    const connectionId = `ws-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const context = this.startWebSocketConnection(connectionId, url, protocols, traceId, parentSpanId);

    // Добавляем обработчики для автоматической трассировки
    ws.addEventListener('message', (event) => {
      const size = typeof event.data === 'string'
        ? new TextEncoder().encode(event.data).length
        : event.data.byteLength || 0;
      this.logWebSocketMessage(context, 'received', 'text', size);
    });

    const originalSend = ws.send.bind(ws);
    ws.send = (data: string | ArrayBufferLike | Blob | ArrayBufferView) => {
      const size = typeof data === 'string'
        ? new TextEncoder().encode(data).length
        : data instanceof ArrayBuffer
          ? data.byteLength
          : 0;
      this.logWebSocketMessage(context, 'sent', 'text', size);
      return originalSend(data);
    };

    ws.addEventListener('close', (event) => {
      this.endWebSocketConnection(context, event.code, event.reason);
    });

    return { ws, context };
  }

  /**
   * Логирует медленную операцию
   * @param operation название операции
   * @param duration длительность в миллисекундах
   * @param slowKind тип медленной операции
   * @param traceId идентификатор трассировки
   * @param spanId идентификатор спана
   * @param parentSpanId родительский span ID
   */
  logSlowOperation(
    operation: string,
    duration: number,
    slowKind: 'autocomplete' | 'chat' | 'http' | 'lsp' | 'server',
    traceId: string,
    spanId: string,
    parentSpanId?: string
  ): void {
    if (!this.config.enabled) return;

    const event = this.createBaseEvent(
      ChatOperationType.SLOW_OPERATION,
      traceId,
      spanId,
      parentSpanId
    );

    Object.assign(event, {
      level: 'WARN',
      [CanonicalFields.OPERATION]: operation,
      [CanonicalFields.DURATION_MS]: duration,
      [CanonicalFields.SLOW_KIND]: slowKind
    });

    this.logEvent(event);
  }

  /**
   * Логирует таймаут операции
   * @param operation название операции
   * @param timeoutType тип таймаута
   * @param duration длительность до таймаута в миллисекундах
   * @param traceId идентификатор трассировки
   * @param spanId идентификатор спана
   * @param parentSpanId родительский span ID
   */
  logTimeout(
    operation: string,
    timeoutType: 'HTTP_TIMEOUT' | 'LSP_TIMEOUT',
    duration: number,
    traceId: string,
    spanId: string,
    parentSpanId?: string
  ): void {
    if (!this.config.enabled) return;

    const event = this.createBaseEvent(
      timeoutType,
      traceId,
      spanId,
      parentSpanId
    );

    Object.assign(event, {
      level: 'ERROR',
      operation,
      duration_ms: duration,
      stop_reason: 'deadline_exceeded'
    });

    this.logEvent(event);
  }

  /**
   * Логирует зависание операции
   * @param operation название операции
   * @param duration длительность зависания в миллисекундах
   * @param hangKind тип зависания
   * @param traceId идентификатор трассировки
   * @param spanId идентификатор спана
   * @param parentSpanId родительский span ID
   */
  logHangDetected(
    operation: string,
    duration: number,
    hangKind: 'ui_stall' | 'io_wait' | 'lock_contention' | 'external_dependency' | 'unknown',
    traceId: string,
    spanId: string,
    parentSpanId?: string
  ): void {
    if (!this.config.enabled) return;

    const event = this.createBaseEvent(
      'HANG_DETECTED',
      traceId,
      spanId,
      parentSpanId
    );

    Object.assign(event, {
      level: 'ERROR',
      [CanonicalFields.OPERATION]: operation,
      [CanonicalFields.DURATION_MS]: duration,
      [CanonicalFields.HANG_KIND]: hangKind
    });

    this.logEvent(event);
  }

  /**
   * Экспортирует телеметрию через все настроенные транспорты
   */
  async exportTelemetry(): Promise<void> {
    await this.transportManager.close();
    await this.browserAdapter.exportTelemetry();
  }

  /**
   * Получает статистику хранилища событий
   */
  getStorageStats(): { eventCount: number; maxEvents: number } {
    // Используем localStorage транспорт для получения статистики
    const localStorageTransport = new LocalStorageTransport({ enabled: true, maxEvents: 1000 });
    return {
      eventCount: localStorageTransport.getEventCount(),
      maxEvents: 1000
    };
  }

  /**
   * Очищает сохраненные события
   */
  clearStoredEvents(): void {
    const localStorageTransport = new LocalStorageTransport({ enabled: true, maxEvents: 1000 });
    localStorageTransport.clearStoredEvents();
  }

  /**
   * Создает экземпляр трассировщика с конфигурацией по умолчанию
   * @return экземпляр ChatTraceUtils
   */
  static createDefault(): ChatTraceUtils {
    // Используем конфигурацию из TraceConfigBrowser вместо дублирования
    const config = TraceConfigBrowser.loadDefaultConfig();
    return new ChatTraceUtils(config);
  }
}

/**
 * Глобальный экземпляр трассировщика для chat-panel
 */
export const chatTracer = ChatTraceUtils.createDefault();

/**
 * Экспорт основных функций для удобства использования
 */
export const generateTraceId = TraceUtilsBase.generateTraceId;
export const generateSpanId = TraceUtilsBase.generateSpanId;
export const generateEventId = () => TraceUtilsBase.generateSpanId(); // Используем spanId как eventId

export {
  ChatOperationType as ChatOp
};