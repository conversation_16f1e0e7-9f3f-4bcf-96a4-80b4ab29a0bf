/**
 * Filin Tracer - Экспорты для Chat Panel
 * 
 * Принцип: Единая точка входа для всех компонентов трассировки
 * Использование: import { chatTracer, ChatOperationType } from './trace'
 */

// Экспорт основных компонентов трассировки
export {
  ChatTraceUtils,
  chatTracer,
  ChatOperationType,
  generateTraceId
} from './TraceUtils';

// Экспорт канонических компонентов трассировки
export {
  CanonicalTraceUtils,
  canonicalChatTracer
} from './CanonicalTraceUtils';

export type {
  CanonicalSpanContext,
  CanonicalSSEStreamContext,
  CanonicalWebSocketContext
} from './CanonicalTraceUtils';

export {
  CanonicalSerializer,
  canonicalSerializer,
  serializeCanonicalEvent,
  validateCanonicalEvent
} from './CanonicalSerializer';

export {
  ChatPanelSchemaValidator,
  chatPanelValidator,
  validateChatEvent,
  validateChatEventSafe
} from './SchemaValidator';

// Экспорт канонических типов
export type {
  CanonicalTraceEvent,
  CanonicalEventType,
  CanonicalGenAIInfo,
  CanonicalChatMessageInfo,
  CanonicalStreamInfo,
  CanonicalWebSocketInfo
} from './CanonicalTypes';

export {
  migrateFieldNames,
  isCanonicalKey,
  validateCanonicalKeys,
  FIELD_MIGRATION_TABLE
} from './CanonicalTypes';

// Экспорт типов для использования в других модулях
export type {
  ChatMessage,
  ChatTraceCommand,
  SSEStreamContext,
  WebSocketTraceContext
} from './TraceUtils';



// Реэкспорт базовых типов из filin-base (безопасные подмодули)
export type {
  TraceConfig,
  TraceEventType,
  MaskingResult
} from 'filin-base/trace';

// Экспорт утилит из filin-base для удобства (только браузерно-безопасные подмодули)
export {
  DataMasker,
  HangDetector
} from 'filin-base/trace';

// Экспорт валидатора схемы (используем подмодуль schema)
export {
  UniversalValidator,
  ValidationMode,
  ValidationErrorType
} from 'filin-base/schema';

export type {
  ValidationResult,
  ValidationError,
  ValidationWarning,
  ValidationContext,
  ValidationMetrics
} from 'filin-base/schema';

// Экспорт браузерных версий для tabby-chat-panel
export { TraceUtilsBase } from './TraceUtilsBrowser';

// Экспорт браузерной версии конфигурации
export * from './TraceConfigBrowser';

// Экспорт React hooks для трассировки
// Примечание: Эти hooks доступны только при использовании React
export {
  useChatTracing,
  useSSETracing,
  useWebSocketTracing,
  useUITracing,
  createTracedClientApi,
  createTracedServerApi,
  BrowserAdapterProvider,
  useBrowserAdapter
} from './ChatTraceHooks';

// Экспорт канонических React hooks
export {
  useCanonicalChatTracing,
  useCanonicalSSETracing,
  useCanonicalWebSocketTracing,
  useCanonicalUITracing,
  createCanonicalTracedClientApi,
  createCanonicalTracedServerApi,
  CanonicalBrowserAdapterProvider,
  useCanonicalBrowserAdapter
} from './CanonicalChatHooks';

// Экспорт BrowserAdapter для прямого использования
export { BrowserAdapter } from '../adapters/BrowserAdapter';
export type { TraceAdapter } from '../adapters/BrowserAdapter';

// Экспорт браузерного генератора ID
export {
  BrowserIdGenerator,
  DeterministicBrowserIdGenerator,
  initializeBrowserIdGenerator
} from "./BrowserIdGenerator";