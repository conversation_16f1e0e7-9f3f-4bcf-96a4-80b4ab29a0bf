/**
 * Console Transport для tabby-chat-panel
 * 
 * Создан на основе backup/transports/ConsoleTransport.ts с адаптацией под браузерное окружение.
 * Выводит события трассировки в консоль браузера с форматированием и цветовым кодированием.
 */

import type { TraceEvent } from 'filin-base/browser';
import type { TraceTransport, ConsoleConfig } from 'filin-base/browser';
import { CanonicalFields } from 'filin-base/browser';

export class ConsoleTransport implements TraceTransport {
  public readonly name = 'console';
  public readonly enabled: boolean;

  private format: 'json' | 'pretty';
  private colorize: boolean;

  constructor(config: ConsoleConfig) {
    this.enabled = config.enabled;
    this.format = config.format || 'json';
    this.colorize = config.colorize !== false; // По умолчанию включено
  }

  async send(event: TraceEvent): Promise<void> {
    if (!this.enabled) return;

    if (this.format === 'pretty') {
      this.logPretty(event);
    } else {
      this.logJson(event);
    }
  }

  async sendBatch(events: TraceEvent[]): Promise<void> {
    if (!this.enabled || events.length === 0) return;

    for (const event of events) {
      await this.send(event);
    }
  }

  async close(): Promise<void> {
    // Console не требует закрытия
  }

  private logJson(event: TraceEvent): void {
    const level = event.level.toLowerCase();
    const logMethod = this.getLogMethod(level);

    if (this.colorize) {
      const style = this.getLogStyle(level);
      logMethod(`%c${JSON.stringify(event)}`, style);
    } else {
      logMethod(JSON.stringify(event));
    }
  }

  private logPretty(event: TraceEvent): void {
    const level = event[CanonicalFields.LEVEL].toLowerCase();
    const logMethod = this.getLogMethod(level);

    const timestamp = new Date(event[CanonicalFields.TIMESTAMP]).toLocaleTimeString();
    const traceInfo = `${event[CanonicalFields.TRACE_ID].substring(0, 8)}...${event[CanonicalFields.SPAN_ID].substring(0, 8)}`;
    const component = event[CanonicalFields.COMPONENT];
    const operation = event[CanonicalFields.OPERATION] || event[CanonicalFields.EVENT];

    let message = `[${timestamp}] ${level.toUpperCase()} [${component}] ${operation}`;

    if (event[CanonicalFields.DURATION_MS] !== undefined) {
      message += ` (${event[CanonicalFields.DURATION_MS]}ms)`;
    }

    if (event[CanonicalFields.ERROR_MESSAGE]) {
      message += ` ERROR: ${event[CanonicalFields.ERROR_MESSAGE]}`;
    }

    message += ` trace=${traceInfo}`;

    if (this.colorize) {
      const style = this.getLogStyle(level);
      logMethod(`%c${message}`, style);

      // Дополнительная информация в сгруппированном виде
      const duration = event[CanonicalFields.DURATION_MS];
      if (level === 'error' || (duration && duration > 1000)) {
        console.group('Details');
        console.log('Full event:', event);
        console.groupEnd();
      }
    } else {
      logMethod(message);
    }
  }

  private getLogMethod(level: string): (...args: any[]) => void {
    switch (level) {
      case 'error':
        return console.error.bind(console);
      case 'warn':
        return console.warn.bind(console);
      case 'debug':
        return console.debug.bind(console);
      case 'info':
      default:
        return console.log.bind(console);
    }
  }

  private getLogStyle(level: string): string {
    if (!this.colorize) return '';

    switch (level) {
      case 'error':
        return 'color: #ff4444; font-weight: bold;';
      case 'warn':
        return 'color: #ffaa00; font-weight: bold;';
      case 'debug':
        return 'color: #888888;';
      case 'info':
      default:
        return 'color: #0088cc;';
    }
  }

  /**
   * Устанавливает формат вывода
   */
  setFormat(format: 'json' | 'pretty'): void {
    this.format = format;
  }

  /**
   * Включает/выключает цветовое кодирование
   */
  setColorize(colorize: boolean): void {
    this.colorize = colorize;
  }

  /**
   * Получает текущие настройки
   */
  getSettings(): { format: string; colorize: boolean } {
    return {
      format: this.format,
      colorize: this.colorize
    };
  }

  /**
   * Проверяет доступность console API
   */
  static isAvailable(): boolean {
    return typeof console !== 'undefined' &&
      typeof console.log === 'function';
  }
}