/**
 * Тесты интеграции CanonicalFields из filin-base
 */

import { describe, it, expect } from 'vitest';
import { CanonicalFields } from "filin-base/browser";
import { CanonicalTraceUtils } from '../CanonicalTraceUtils';
import { CanonicalTraceEvent } from '../CanonicalTypes';

describe('CanonicalFields Integration', () => {
  it('should import CanonicalFields from filin-base', () => {
    expect(CanonicalFields).toBeDefined();
    expect(CanonicalFields.TRACE_ID).toBe('trace.id');
    expect(CanonicalFields.SPAN_ID).toBe('span.id');
    expect(CanonicalFields.GEN_AI_REQUEST_MODEL).toBe('gen_ai.request.model');
    expect(CanonicalFields.GEN_AI_USAGE_INPUT_TOKENS).toBe('gen_ai.usage.input_tokens');
    expect(CanonicalFields.GEN_AI_USAGE_OUTPUT_TOKENS).toBe('gen_ai.usage.output_tokens');
  });

  it('should use CanonicalFields constants in trace events', () => {
    const traceUtils = CanonicalTraceUtils.createDefault();
    const context = traceUtils.startSpan('test_operation');

    expect(context[CanonicalFields.TRACE_ID]).toBeDefined();
    expect(context[CanonicalFields.SPAN_ID]).toBeDefined();
    expect(typeof context[CanonicalFields.TRACE_ID]).toBe('string');
    expect(typeof context[CanonicalFields.SPAN_ID]).toBe('string');
  });

  it('should create canonical events with proper field names', () => {
    const event: CanonicalTraceEvent = {
      [CanonicalFields.TIMESTAMP]: '2025-01-15T10:00:00.000Z',
      [CanonicalFields.TIMESTAMP_UNIX_NANOS]: '1736936400000000000',
      [CanonicalFields.EVENT_ID]: '01936d4a-1234-7890-abcd-123456789012',
      [CanonicalFields.TRACE_ID]: 'abcdef1234567890abcdef1234567890',
      [CanonicalFields.SPAN_ID]: 'abcdef1234567890',
      [CanonicalFields.LEVEL]: 'INFO',
      [CanonicalFields.EVENT]: 'CHAT_MESSAGE_SENT',
      [CanonicalFields.EVENT_VERSION]: '1.0',
      [CanonicalFields.SCHEMA_VERSION]: 'filin.log.v1',
      [CanonicalFields.SERVICE_NAME]: 'tabby-chat-panel',
      [CanonicalFields.SERVICE_VERSION]: '0.10.0',
      [CanonicalFields.SERVICE_INSTANCE_ID]: 'chat-panel-001',
      [CanonicalFields.SERVICE_NAMESPACE]: 'filin.chat',
      [CanonicalFields.DEPLOYMENT_ENVIRONMENT]: 'development',
      [CanonicalFields.COMPONENT]: 'chat-handler',
      [CanonicalFields.GEN_AI_REQUEST_MODEL]: 'gpt-4',
      [CanonicalFields.GEN_AI_USAGE_INPUT_TOKENS]: 100,
      [CanonicalFields.GEN_AI_USAGE_OUTPUT_TOKENS]: 50
    };

    expect(event[CanonicalFields.GEN_AI_REQUEST_MODEL]).toBe('gpt-4');
    expect(event[CanonicalFields.GEN_AI_USAGE_INPUT_TOKENS]).toBe(100);
    expect(event[CanonicalFields.GEN_AI_USAGE_OUTPUT_TOKENS]).toBe(50);
  });

  it('should use constants in GenAI info interface', () => {
    const genAIInfo = {
      [CanonicalFields.GEN_AI_PROVIDER_NAME]: 'openai',
      [CanonicalFields.GEN_AI_REQUEST_MODEL]: 'gpt-4',
      [CanonicalFields.GEN_AI_USAGE_INPUT_TOKENS]: 100,
      [CanonicalFields.GEN_AI_USAGE_OUTPUT_TOKENS]: 50
    };

    expect(genAIInfo[CanonicalFields.GEN_AI_PROVIDER_NAME]).toBe('openai');
    expect(genAIInfo[CanonicalFields.GEN_AI_REQUEST_MODEL]).toBe('gpt-4');
    expect(genAIInfo[CanonicalFields.GEN_AI_USAGE_INPUT_TOKENS]).toBe(100);
    expect(genAIInfo[CanonicalFields.GEN_AI_USAGE_OUTPUT_TOKENS]).toBe(50);
  });
});