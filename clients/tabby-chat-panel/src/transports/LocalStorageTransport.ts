/**
 * LocalStorage Transport для tabby-chat-panel
 * 
 * Создан на основе backup/transports/LocalStorageTransport.ts с адаптацией под новую структуру.
 * Убраны проверки browser API (всегда доступны в браузере).
 * 
 * Сохраняет события трассировки в localStorage для персистентности между сессиями.
 */

import type { TraceEvent } from 'filin-base/browser';
import type { TraceTransport, TransportConfig } from 'filin-base/browser';

export interface LocalStorageConfig extends TransportConfig {
  maxEvents?: number;
  storageKey?: string;
}

export class LocalStorageTransport implements TraceTransport {
  public readonly name = 'localStorage';
  public readonly enabled: boolean;

  private readonly maxEvents: number;
  private readonly storageKey: string;

  constructor(config: LocalStorageConfig) {
    this.enabled = config.enabled;
    this.maxEvents = config.maxEvents || 1000;
    this.storageKey = config.storageKey || 'filin-trace-events';
  }

  async send(event: TraceEvent): Promise<void> {
    if (!this.enabled) {
      return;
    }

    try {
      const events = this.getStoredEvents();
      events.push(event);

      // Ограничиваем количество событий
      if (events.length > this.maxEvents) {
        events.splice(0, events.length - this.maxEvents);
      }

      localStorage.setItem(this.storageKey, JSON.stringify(events));
    } catch (error) {
      console.warn('[LocalStorageTransport] Failed to store event:', error);
    }
  }

  async sendBatch(events: TraceEvent[]): Promise<void> {
    if (!this.enabled || events.length === 0) {
      return;
    }

    try {
      const storedEvents = this.getStoredEvents();
      storedEvents.push(...events);

      // Ограничиваем количество событий
      if (storedEvents.length > this.maxEvents) {
        storedEvents.splice(0, storedEvents.length - this.maxEvents);
      }

      localStorage.setItem(this.storageKey, JSON.stringify(storedEvents));
    } catch (error) {
      console.warn('[LocalStorageTransport] Failed to store batch:', error);
    }
  }

  async close(): Promise<void> {
    // LocalStorage не требует закрытия
  }

  /**
   * Получает сохраненные события
   */
  getStoredEvents(): TraceEvent[] {
    try {
      const stored = localStorage.getItem(this.storageKey);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.warn('[LocalStorageTransport] Failed to retrieve events:', error);
      return [];
    }
  }

  /**
   * Очищает сохраненные события
   */
  clearStoredEvents(): void {
    try {
      localStorage.removeItem(this.storageKey);
    } catch (error) {
      console.warn('[LocalStorageTransport] Failed to clear events:', error);
    }
  }

  /**
   * Получает количество сохраненных событий
   */
  getEventCount(): number {
    return this.getStoredEvents().length;
  }

  /**
   * Экспортирует события в JSON Lines формате
   */
  exportEvents(): string {
    const events = this.getStoredEvents();
    return events.map(event => JSON.stringify(event)).join('\n');
  }

  /**
   * Получает статистику хранилища
   */
  getStorageStats(): { eventCount: number; maxEvents: number; storageUsed: number } {
    const events = this.getStoredEvents();
    const storageUsed = new Blob([JSON.stringify(events)]).size;

    return {
      eventCount: events.length,
      maxEvents: this.maxEvents,
      storageUsed
    };
  }

  /**
   * Проверяет доступность localStorage
   */
  static isAvailable(): boolean {
    if (typeof window === 'undefined' || typeof localStorage === 'undefined') {
      return false;
    }
    try {
      const test = '__localStorage_test__';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch {
      return false;
    }
  }
}