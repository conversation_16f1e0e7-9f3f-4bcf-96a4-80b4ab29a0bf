/**
 * OTLP Transport для tabby-chat-panel
 * 
 * Реализует отправку трейсов в формате OpenTelemetry Protocol (OTLP) через HTTP
 * для интеграции с <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> и другими системами мониторинга.
 * 
 * Конфигурация берется из TraceConfig вместо переменных окружения
 * (так как в браузере нет доступа к process.env).
 */

import type { TraceEvent } from 'filin-base/browser';
import { CanonicalFields } from 'filin-base/browser';
import type { TraceTransport, OTLPConfig } from 'filin-base/browser';
import { TraceConfigBrowser } from '../trace/TraceConfigBrowser';

export interface OTLPTransportConfig extends OTLPConfig {
  endpoint: string;
  protocol: 'http'; // В браузере поддерживаем только HTTP
  headers?: Record<string, string>;
  compression?: 'gzip' | 'none';
  batchSize?: number;
  batchTimeout?: number;
  maxRetries?: number;
  retryDelay?: number;
}

/**
 * OTLP Span для отправки в коллектор
 */
interface OTLPSpan {
  traceId: string;
  spanId: string;
  parentSpanId?: string;
  name: string;
  kind: number; // SPAN_KIND_INTERNAL = 1
  startTimeUnixNano: string;
  endTimeUnixNano?: string;
  attributes: Array<{ key: string; value: { stringValue?: string; intValue?: string; boolValue?: boolean } }>;
  status?: { code: number; message?: string };
  events?: Array<{
    timeUnixNano: string;
    name: string;
    attributes: Array<{ key: string; value: { stringValue?: string; intValue?: string; boolValue?: boolean } }>;
  }>;
}

/**
 * OTLP Resource для описания сервиса
 */
interface OTLPResource {
  attributes: Array<{ key: string; value: { stringValue?: string; intValue?: string; boolValue?: boolean } }>;
}

/**
 * OTLP Payload для отправки
 */
interface OTLPPayload {
  resourceSpans: Array<{
    resource: OTLPResource;
    scopeSpans: Array<{
      scope: {
        name: string;
        version: string;
      };
      spans: OTLPSpan[];
    }>;
  }>;
}

export class OTLPTransport implements TraceTransport {
  public readonly name = 'otlp';
  public readonly enabled: boolean;

  private config: OTLPTransportConfig;
  private batchBuffer: TraceEvent[] = [];
  private batchTimer: number | null = null;
  private spanBuffer = new Map<string, { start?: TraceEvent; events: TraceEvent[] }>();

  constructor(config: OTLPTransportConfig) {
    // Объединяем переданную конфигурацию с переменными окружения
    const envConfig = this.parseOTELConfig();
    const mergedConfig = {
      compression: 'none' as const,
      batchSize: 50,
      batchTimeout: 10000,
      maxRetries: 3,
      retryDelay: 1000,
      ...envConfig,
      ...config,
      protocol: config.protocol || envConfig.protocol || 'http' as const
    };

    this.config = mergedConfig;
    this.enabled = config.enabled && !!mergedConfig.endpoint;
  }

  async send(event: TraceEvent): Promise<void> {
    if (!this.enabled) return;

    // Добавляем в батч
    this.batchBuffer.push(event);

    // Если батч заполнен, отправляем немедленно
    if (this.batchBuffer.length >= (this.config.batchSize || 50)) {
      await this.flushBatch();
    } else {
      // Устанавливаем таймер для отправки батча
      this.scheduleBatchFlush();
    }
  }

  async sendBatch(events: TraceEvent[]): Promise<void> {
    if (!this.enabled || events.length === 0) return;

    // Группируем события по спанам
    this.groupEventsBySpans(events);

    // Конвертируем в OTLP формат
    const otlpPayload = this.convertToOTLP();

    // Отправляем с retry логикой
    await this.retryWithBackoff(() => this.sendOTLPRequest(otlpPayload));
  }

  private groupEventsBySpans(events: TraceEvent[]): void {
    for (const event of events) {
      const spanKey = `${event['trace.id']}-${event['span.id']}`;

      if (!this.spanBuffer.has(spanKey)) {
        this.spanBuffer.set(spanKey, { events: [] });
      }

      const spanData = this.spanBuffer.get(spanKey)!;

      if (event.event === 'SPAN_START') {
        spanData.start = event;
      } else {
        spanData.events.push(event);
      }
    }
  }

  private convertToOTLP(): OTLPPayload {
    const spans: OTLPSpan[] = [];

    for (const [, spanData] of this.spanBuffer) {
      if (!spanData.start) continue; // Пропускаем спаны без SPAN_START

      const startEvent = spanData.start;
      const endEvent = spanData.events.find((e: TraceEvent) => e.event === 'SPAN_END');

      const span: OTLPSpan = {
        traceId: this.hexToBase64(startEvent[CanonicalFields.TRACE_ID]),
        spanId: this.hexToBase64(startEvent[CanonicalFields.SPAN_ID]),
        parentSpanId: startEvent[CanonicalFields.PARENT_SPAN_ID] ?
          this.hexToBase64(startEvent[CanonicalFields.PARENT_SPAN_ID] as string) : undefined,
        name: startEvent.operation || startEvent.component || startEvent.event,
        kind: 1, // SPAN_KIND_INTERNAL
        startTimeUnixNano: startEvent[CanonicalFields.TIMESTAMP_UNIX_NANOS],
        endTimeUnixNano: endEvent?.[CanonicalFields.TIMESTAMP_UNIX_NANOS],
        attributes: this.convertAttributes(startEvent),
        events: spanData.events
          .filter((e: TraceEvent) => e.event !== 'SPAN_END')
          .map((e: TraceEvent) => ({
            timeUnixNano: e[CanonicalFields.TIMESTAMP_UNIX_NANOS],
            name: e.event,
            attributes: this.convertAttributes(e)
          }))
      };

      if (endEvent?.[CanonicalFields.STOP_REASON] === 'error' || endEvent?.[CanonicalFields.ERROR_MESSAGE]) {
        span.status = {
          code: 2, // STATUS_CODE_ERROR
          message: endEvent[CanonicalFields.ERROR_MESSAGE] as string
        };
      }

      spans.push(span);
    }

    // Очищаем буфер после конвертации
    this.spanBuffer.clear();

    // Получаем service info из конфигурации
    const serviceInfo = TraceConfigBrowser.getServiceInfo();
    const serviceName = serviceInfo.name; // Используем имя из конфигурации вместо переменной окружения

    return {
      resourceSpans: [{
        resource: {
          attributes: [
            { key: 'service.name', value: { stringValue: serviceName } },
            { key: 'service.version', value: { stringValue: serviceInfo.version } },
            { key: 'service.namespace', value: { stringValue: serviceInfo.namespace } },
            { key: 'deployment.environment', value: { stringValue: serviceInfo.environment } }
          ]
        },
        scopeSpans: [{
          scope: {
            name: 'filin-trace',
            version: serviceInfo.version
          },
          spans
        }]
      }]
    };
  }

  private convertAttributes(event: TraceEvent): Array<{ key: string; value: { stringValue?: string; intValue?: string; boolValue?: boolean } }> {
    const attributes: Array<{ key: string; value: { stringValue?: string; intValue?: string; boolValue?: boolean } }> = [];

    // Конвертируем основные поля
    const fieldsToConvert = [
      CanonicalFields.COMPONENT,
      CanonicalFields.OPERATION,
      CanonicalFields.DIRECTION,
      CanonicalFields.STOP_REASON,
      CanonicalFields.DATA_CLASSIFICATION,
      CanonicalFields.HTTP_REQUEST_METHOD,
      CanonicalFields.URL_PATH,
      CanonicalFields.RPC_METHOD,
      CanonicalFields.GEN_AI_REQUEST_MODEL,
      CanonicalFields.ERROR_TYPE,
      CanonicalFields.ERROR_MESSAGE
    ];

    for (const field of fieldsToConvert) {
      const value = (event as any)[field];
      if (value !== undefined) {
        if (typeof value === 'string') {
          attributes.push({ key: field, value: { stringValue: value } });
        } else if (typeof value === 'number') {
          attributes.push({ key: field, value: { intValue: value.toString() } });
        } else if (typeof value === 'boolean') {
          attributes.push({ key: field, value: { boolValue: value } });
        }
      }
    }

    // Добавляем числовые метрики
    const numericFields = [
      CanonicalFields.DURATION_MS,
      CanonicalFields.HTTP_RESPONSE_STATUS_CODE,
      CanonicalFields.TIME_TO_FIRST_TOKEN,
      CanonicalFields.TIME_TO_LAST_BYTE
    ];
    for (const field of numericFields) {
      const value = (event as any)[field];
      if (typeof value === 'number') {
        attributes.push({ key: field, value: { intValue: value.toString() } });
      }
    }

    return attributes;
  }

  private hexToBase64(hex: string): string {
    // Нормализуем hex строку
    let normalizedHex = hex.replace(/[^0-9a-fA-F]/g, '');

    // Определяем длину: trace ID = 32 символа, span ID = 16 символов
    if (normalizedHex.length <= 16) {
      normalizedHex = normalizedHex.padStart(16, '0');
    } else {
      normalizedHex = normalizedHex.padStart(32, '0').substring(0, 32);
    }

    // Конвертируем в байты
    const bytes = new Uint8Array(normalizedHex.length / 2);
    for (let i = 0; i < normalizedHex.length; i += 2) {
      bytes[i / 2] = parseInt(normalizedHex.substring(i, i + 2), 16);
    }

    return btoa(String.fromCharCode(...bytes));
  }

  private async sendOTLPRequest(payload: OTLPPayload): Promise<void> {
    const serviceInfo = TraceConfigBrowser.getServiceInfo();
    const headers = {
      'Content-Type': 'application/json',
      'User-Agent': `${serviceInfo.name}/${serviceInfo.version}`,
      ...this.config.headers,
    };

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 секунд таймаут

    try {
      const response = await fetch(`${this.config.endpoint}/v1/traces`, {
        method: 'POST',
        headers,
        body: JSON.stringify(payload),
        signal: controller.signal,
      });

      if (!response.ok) {
        throw new Error(`OTLP HTTP ${response.status}: ${response.statusText}`);
      }
    } finally {
      clearTimeout(timeoutId);
    }
  }

  private scheduleBatchFlush(): void {
    if (this.batchTimer) return;

    const timeout = this.config.batchTimeout || 10000;
    this.batchTimer = window.setTimeout(async () => {
      await this.flushBatch();
    }, timeout);
  }

  private async flushBatch(): Promise<void> {
    if (this.batchTimer) {
      window.clearTimeout(this.batchTimer);
      this.batchTimer = null;
    }

    if (this.batchBuffer.length === 0) return;

    const events = [...this.batchBuffer];
    this.batchBuffer = [];

    try {
      await this.sendBatch(events);
    } catch (error) {
      console.error('[OTLPTransport] Failed to send batch:', error);
      throw error;
    }
  }

  /**
   * Retry логика с exponential backoff
   */
  private async retryWithBackoff<T>(
    operation: () => Promise<T>,
    maxRetries: number = this.config.maxRetries || 3,
    baseDelay: number = this.config.retryDelay || 1000
  ): Promise<T> {
    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;

        if (attempt < maxRetries) {
          const delay = baseDelay * Math.pow(2, attempt);
          await this.sleep(delay);
        }
      }
    }

    throw lastError;
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Парсит конфигурацию OTLP из TraceConfig вместо переменных окружения
   */
  private parseOTELConfig(): Partial<OTLPTransportConfig> {
    const config: Partial<OTLPTransportConfig> = {};
    const traceConfig = TraceConfigBrowser.loadDefaultConfig();

    // Endpoint из конфигурации
    if (traceConfig.otlp?.endpoint) {
      config.endpoint = traceConfig.otlp.endpoint;
    }

    // Protocol (в браузере всегда HTTP)
    config.protocol = 'http';

    // Headers из конфигурации
    if (traceConfig.otlp?.headers) {
      config.headers = traceConfig.otlp.headers;
    }

    return config;
  }

  async close(): Promise<void> {
    if (this.batchTimer) {
      window.clearTimeout(this.batchTimer);
      this.batchTimer = null;
    }

    // Отправляем оставшиеся события
    if (this.batchBuffer.length > 0) {
      await this.flushBatch();
    }
  }

  /**
   * Принудительная отправка накопленных событий
   */
  async flush(): Promise<void> {
    await this.flushBatch();
  }

  /**
   * Получает статистику батча
   */
  getBatchStats(): { pendingEvents: number; pendingSpans: number; batchSize: number; batchTimeout: number } {
    return {
      pendingEvents: this.batchBuffer.length,
      pendingSpans: this.spanBuffer.size,
      batchSize: this.config.batchSize || 50,
      batchTimeout: this.config.batchTimeout || 10000
    };
  }

  /**
   * Проверяет доступность Fetch API для OTLP
   */
  static isAvailable(): boolean {
    return typeof window !== 'undefined' && typeof fetch !== 'undefined';
  }

  /**
   * Создает конфигурацию для Jaeger
   */
  static createJaegerConfig(jaegerEndpoint: string): OTLPTransportConfig {
    return {
      enabled: true,
      endpoint: jaegerEndpoint,
      protocol: 'http',
      headers: {
        'Content-Type': 'application/json'
      },
      batchSize: 50,
      batchTimeout: 10000,
      maxRetries: 3,
      retryDelay: 1000
    };
  }

  /**
   * Создает конфигурацию для Grafana/OTEL Collector
   */
  static createGrafanaConfig(grafanaEndpoint: string, apiKey?: string): OTLPTransportConfig {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };

    if (apiKey) {
      headers['Authorization'] = `Bearer ${apiKey}`;
    }

    return {
      enabled: true,
      endpoint: grafanaEndpoint,
      protocol: 'http',
      headers,
      batchSize: 100,
      batchTimeout: 5000,
      maxRetries: 5,
      retryDelay: 2000
    };
  }
}