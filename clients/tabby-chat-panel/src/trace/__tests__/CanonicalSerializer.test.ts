/**
 * Тесты для канонического сериализатора tabby-chat-panel
 */

import { describe, it, expect } from 'vitest';
import { CanonicalFields } from 'filin-base/browser';
import { CanonicalSerializer, serializeCanonicalEvent, validateCanonicalEvent } from '../CanonicalSerializer';

describe('CanonicalSerializer', () => {
  describe('serialize', () => {
    it('should serialize event with canonical field names', () => {
      const serializer = new CanonicalSerializer({ validateKeys: false });
      
      const event = {
        traceId: 'abc123',
        'model.name': 'gpt-4',
        'prompt.tokens': 100,
        nullField: null,
        undefinedField: undefined
      };

      const result = serializer.serialize(event);

      expect(result.canonicalEvent).toEqual({
        [CanonicalFields.TRACE_ID]: 'abc123',
        [CanonicalFields.GEN_AI_REQUEST_MODEL]: 'gpt-4',
        [CanonicalFields.GEN_AI_USAGE_INPUT_TOKENS]: 100
      });

      const parsed = JSON.parse(result.json);
      expect(parsed).toEqual({
        [CanonicalFields.TRACE_ID]: 'abc123',
        [CanonicalFields.GEN_AI_REQUEST_MODEL]: 'gpt-4',
        [CanonicalFields.GEN_AI_USAGE_INPUT_TOKENS]: 100
      });
    });

    it('should remove null and undefined fields', () => {
      const serializer = new CanonicalSerializer({ validateKeys: false });
      
      const event = {
        [CanonicalFields.TRACE_ID]: 'abc123',
        [CanonicalFields.GEN_AI_REQUEST_MODEL]: null,
        [CanonicalFields.GEN_AI_USAGE_INPUT_TOKENS]: undefined,
        'valid.field': 'value'
      };

      const result = serializer.serialize(event);

      expect(result.canonicalEvent).toEqual({
        [CanonicalFields.TRACE_ID]: 'abc123',
        'valid.field': 'value'
      });
    });

    it('should handle nested objects with null removal', () => {
      const serializer = new CanonicalSerializer({ validateKeys: false });
      
      const event = {
        [CanonicalFields.TRACE_ID]: 'abc123',
        nested: {
          'valid.field': 'value',
          nullField: null,
          deepNested: {
            'another.field': 'value2',
            nullDeep: null
          }
        }
      };

      const result = serializer.serialize(event);

      expect(result.canonicalEvent).toEqual({
        [CanonicalFields.TRACE_ID]: 'abc123',
        nested: {
          'valid.field': 'value',
          deepNested: {
            'another.field': 'value2'
          }
        }
      });
    });

    it('should validate canonical keys when enabled', () => {
      const serializer = new CanonicalSerializer({ 
        validateKeys: true, 
        failOnNonCanonical: true 
      });
      
      const event = {
        traceId: 'abc123', // Non-canonical
        [CanonicalFields.SPAN_ID]: 'def456' // Canonical
      };

      expect(() => serializer.serialize(event)).toThrow('Non-canonical keys found');
    });

    it('should generate warnings for non-canonical keys when not failing', () => {
      const serializer = new CanonicalSerializer({ 
        validateKeys: true, 
        failOnNonCanonical: false,
        includeWarnings: true
      });
      
      const event = {
        traceId: 'abc123',
        [CanonicalFields.SPAN_ID]: 'def456'
      };

      const result = serializer.serialize(event);

      expect(result.warnings).toBeDefined();
      expect(result.warnings![0]).toContain('Non-canonical keys found');
    });

    it('should pretty print when enabled', () => {
      const serializer = new CanonicalSerializer({ 
        validateKeys: false,
        prettyPrint: true
      });
      
      const event = {
        [CanonicalFields.TRACE_ID]: 'abc123',
        [CanonicalFields.SPAN_ID]: 'def456'
      };

      const result = serializer.serialize(event);

      expect(result.json).toContain('\n');
      expect(result.json).toContain('  ');
    });
  });

  describe('serializeBatch', () => {
    it('should serialize multiple events', () => {
      const serializer = new CanonicalSerializer({ validateKeys: false });
      
      const events = [
        { traceId: 'abc123', 'model.name': 'gpt-4' },
        { traceId: 'def456', 'model.name': 'claude-3' }
      ];

      const results = serializer.serializeBatch(events);

      expect(results).toHaveLength(2);
      expect(results[0].canonicalEvent[CanonicalFields.TRACE_ID]).toBe('abc123');
      expect(results[0].canonicalEvent[CanonicalFields.GEN_AI_REQUEST_MODEL]).toBe('gpt-4');
      expect(results[1].canonicalEvent[CanonicalFields.TRACE_ID]).toBe('def456');
      expect(results[1].canonicalEvent[CanonicalFields.GEN_AI_REQUEST_MODEL]).toBe('claude-3');
    });
  });

  describe('serializeToJSONLines', () => {
    it('should serialize to JSON Lines format', () => {
      const serializer = new CanonicalSerializer({ validateKeys: false });
      
      const event = {
        traceId: 'abc123',
        'model.name': 'gpt-4'
      };

      const jsonLine = serializer.serializeToJSONLines(event);

      expect(jsonLine).toBe(`{"${CanonicalFields.TRACE_ID}":"abc123","${CanonicalFields.GEN_AI_REQUEST_MODEL}":"gpt-4"}`);
    });
  });

  describe('serializeBatchToJSONLines', () => {
    it('should serialize batch to JSON Lines format', () => {
      const serializer = new CanonicalSerializer({ validateKeys: false });
      
      const events = [
        { traceId: 'abc123', 'model.name': 'gpt-4' },
        { traceId: 'def456', 'model.name': 'claude-3' }
      ];

      const jsonLines = serializer.serializeBatchToJSONLines(events);

      const lines = jsonLines.split('\n');
      expect(lines).toHaveLength(2);
      expect(lines[0]).toBe(`{"${CanonicalFields.TRACE_ID}":"abc123","${CanonicalFields.GEN_AI_REQUEST_MODEL}":"gpt-4"}`);
      expect(lines[1]).toBe(`{"${CanonicalFields.TRACE_ID}":"def456","${CanonicalFields.GEN_AI_REQUEST_MODEL}":"claude-3"}`);
    });
  });

  describe('validate', () => {
    it('should validate canonical event successfully', () => {
      const serializer = new CanonicalSerializer();
      
      const event = {
        [CanonicalFields.TRACE_ID]: 'abc123',
        [CanonicalFields.SPAN_ID]: 'def456',
        [CanonicalFields.GEN_AI_REQUEST_MODEL]: 'gpt-4'
      };

      const result = serializer.validate(event);

      expect(result.valid).toBe(true);
      expect(result.errors).toBeUndefined();
    });

    it('should detect validation errors', () => {
      const serializer = new CanonicalSerializer();
      
      const event = {
        traceId: 'abc123', // Non-canonical
        [CanonicalFields.SPAN_ID]: 'def456'
      };

      const result = serializer.validate(event);

      expect(result.valid).toBe(false);
      expect(result.errors).toBeDefined();
      expect(result.errors![0]).toContain('Non-canonical keys');
    });
  });

  describe('static factory methods', () => {
    it('should create development serializer', () => {
      const serializer = CanonicalSerializer.createDevelopmentSerializer();
      const options = serializer.getOptions();

      expect(options.validateKeys).toBe(true);
      expect(options.failOnNonCanonical).toBe(true);
      expect(options.includeWarnings).toBe(true);
    });

    it('should create production serializer', () => {
      const serializer = CanonicalSerializer.createProductionSerializer();
      const options = serializer.getOptions();

      expect(options.validateKeys).toBe(false);
      expect(options.failOnNonCanonical).toBe(false);
      expect(options.includeWarnings).toBe(false);
    });

    it('should create test serializer', () => {
      const serializer = CanonicalSerializer.createTestSerializer();
      const options = serializer.getOptions();

      expect(options.validateKeys).toBe(true);
      expect(options.failOnNonCanonical).toBe(true);
      expect(options.includeWarnings).toBe(true);
      expect(options.prettyPrint).toBe(true);
    });
  });

  describe('utility functions', () => {
    it('should serialize canonical event with utility function', () => {
      const event = {
        [CanonicalFields.TRACE_ID]: 'abc123',
        [CanonicalFields.GEN_AI_REQUEST_MODEL]: 'gpt-4'
      };

      const jsonLine = serializeCanonicalEvent(event);

      expect(jsonLine).toBe(`{"${CanonicalFields.TRACE_ID}":"abc123","${CanonicalFields.GEN_AI_REQUEST_MODEL}":"gpt-4"}`);
    });

    it('should validate canonical event with utility function', () => {
      const event = {
        [CanonicalFields.TRACE_ID]: 'abc123',
        [CanonicalFields.GEN_AI_REQUEST_MODEL]: 'gpt-4'
      };

      const result = validateCanonicalEvent(event);

      expect(result.valid).toBe(true);
    });
  });

  describe('updateOptions', () => {
    it('should update serializer options', () => {
      const serializer = new CanonicalSerializer({ validateKeys: false });
      
      serializer.updateOptions({ validateKeys: true, prettyPrint: true });
      const options = serializer.getOptions();

      expect(options.validateKeys).toBe(true);
      expect(options.prettyPrint).toBe(true);
    });
  });
});