/**
 * Tabby Chat Panel - Браузерный загрузчик конфигурации
 * 
 * Специализированная версия для браузерной среды, которая читает только ENV переменные
 * и не использует файловую систему
 */
import type { TraceConfig } from 'filin-base/browser';

/**
 * Ошибка валидации конфигурации
 */
export class ConfigValidationError extends Error {
  constructor(message: string, public field?: string) {
    super(message);
    this.name = 'ConfigValidationError';
  }
}

/**
 * Браузерный загрузчик конфигурации для tabby-chat-panel
 */
export class ConfigLoader {
  private static cachedConfig: TraceConfig | null = null;
  
  /**
   * Загружает конфигурацию трассировки (только ENV переменные)
   * @return конфигурация трассировки
   */
  static loadConfig(): TraceConfig {
    if (ConfigLoader.cachedConfig) {
      return ConfigLoader.cachedConfig;
    }
    
    const defaultConfig = ConfigLoader.getDefaultConfig();
    
    // В браузере читаем только переменные окружения (если доступны через process.env)
    const enabled = ConfigLoader.getEnvBoolean('FILIN_TRACE_ENABLED', defaultConfig.enabled);
    const level = ConfigLoader.getEnvString('FILIN_TRACE_LEVEL') || defaultConfig.level;
    
    // Пороги операций с валидацией
    const autocompleteSlowMs = ConfigLoader.getEnvNumber('FILIN_AUTOCOMPLETE_SLOW_MS', defaultConfig.autocompleteSlowMs);
    const chatSlowMs = ConfigLoader.getEnvNumber('FILIN_CHAT_SLOW_MS', defaultConfig.chatSlowMs);
    const httpTimeoutMs = ConfigLoader.getEnvNumber('FILIN_HTTP_TIMEOUT_MS', defaultConfig.httpTimeoutMs);
    const lspTimeoutMs = ConfigLoader.getEnvNumber('FILIN_LSP_TIMEOUT_MS', defaultConfig.lspTimeoutMs);
    const slowOperationMs = ConfigLoader.getEnvNumber('FILIN_SLOW_OPERATION_MS', defaultConfig.slowOperationMs);
    const operationHangMs = ConfigLoader.getEnvNumber('FILIN_OPERATION_HANG_MS', defaultConfig.operationHangMs);
    
    // Тестовый режим и дополнительные флаги
    const testMode = ConfigLoader.getEnvBoolean('FILIN_TEST_MODE', false);
    const streamFullLog = ConfigLoader.getEnvBoolean('FILIN_STREAM_FULL_LOG', false);
    
    // Валидация конфигурации
    ConfigLoader.validateConfig({
      autocompleteSlowMs,
      chatSlowMs,
      httpTimeoutMs,
      lspTimeoutMs,
      slowOperationMs,
      operationHangMs
    });
    
    ConfigLoader.cachedConfig = {
      enabled,
      level,
      slowOperationMs,
      httpTimeoutMs,
      lspTimeoutMs,
      operationHangMs,
      maskTokens: true, // По умолчанию включено в браузере
      maskPatterns: defaultConfig.maskPatterns,
      
      // Новые поля
      autocompleteSlowMs,
      chatSlowMs,
      testMode,
      streamFullLog,
      
      // OTLP конфигурация (упрощенная для браузера)
      otlp: {
        endpoint: ConfigLoader.getEnvString('OTEL_EXPORTER_OTLP_ENDPOINT') || 'http://localhost:4317',
        headers: ConfigLoader.parseOtlpHeaders(ConfigLoader.getEnvString('OTEL_EXPORTER_OTLP_HEADERS')) || {}
      },
      
      // Sampling
      sampling: {
        ratio: ConfigLoader.parseSamplingRatio(ConfigLoader.getEnvString('OTEL_TRACES_SAMPLER_ARG')),
        forceSampleOnError: true
      },
      
      // Streaming
      streaming: {
        logEveryNChunks: ConfigLoader.getEnvNumber('FILIN_STREAM_LOG_EVERY_N', 10),
        maxChunksLogged: ConfigLoader.getEnvNumber('FILIN_STREAM_MAX_CHUNKS', 100)
      }
    };
    
    return ConfigLoader.cachedConfig;
  }
  
  /**
   * Возвращает конфигурацию по умолчанию для браузера
   */
  private static getDefaultConfig(): TraceConfig {
    return {
      enabled: false,  // По умолчанию выключено
      level: 'INFO',
      slowOperationMs: 5000,
      httpTimeoutMs: 20000,
      lspTimeoutMs: 30000,
      operationHangMs: 10000,
      maskTokens: true,
      maskPatterns: [
        'Bearer\\s+[A-Za-z0-9\\-_]+',
        'token["\']?\\s*[:=]\\s*["\'][^"\']+["\']',
        'api[_-]?key["\']?\\s*[:=]\\s*["\'][^"\']+["\']',
        'password["\']?\\s*[:=]\\s*["\'][^"\']+["\']',
        'secret["\']?\\s*[:=]\\s*["\'][^"\']+["\']',
        'sk-[A-Za-z0-9]{48}',
        '[A-Za-z0-9]{32,}'
      ],
      
      // Новые поля
      autocompleteSlowMs: 1000,
      chatSlowMs: 3000,
      testMode: false,
      streamFullLog: false,
      
      // OTLP конфигурация
      otlp: {
        endpoint: 'http://localhost:4317',
        headers: {}
      },
      
      // Sampling
      sampling: {
        ratio: 1.0,
        forceSampleOnError: true
      },
      
      // Streaming
      streaming: {
        logEveryNChunks: 10,
        maxChunksLogged: 100
      }
    };
  }
  
  /**
   * Получает строковое значение из переменной окружения (безопасно для браузера)
   */
  private static getEnvString(envVar: string): string | undefined {
    if (typeof process !== 'undefined' && process.env) {
      return process.env[envVar];
    }
    return undefined;
  }
  
  /**
   * Получает boolean значение из переменной окружения (безопасно для браузера)
   */
  private static getEnvBoolean(envVar: string, defaultValue: boolean): boolean {
    const value = ConfigLoader.getEnvString(envVar);
    if (value === undefined) return defaultValue;
    return value.toLowerCase() === 'true' || value === '1';
  }
  
  /**
   * Получает number значение из переменной окружения с валидацией (безопасно для браузера)
   */
  private static getEnvNumber(envVar: string, defaultValue: number): number {
    const value = ConfigLoader.getEnvString(envVar);
    if (value === undefined) return defaultValue;
    const parsed = parseInt(value, 10);
    if (isNaN(parsed) || parsed < 0) {
      console.warn(`[FilinTracer] Invalid value for ${envVar}: ${value}, using default: ${defaultValue}`);
      return defaultValue;
    }
    return parsed;
  }
  
  /**
   * Парсит OTLP заголовки из переменной окружения
   */
  private static parseOtlpHeaders(headersStr?: string): Record<string, string> | null {
    if (!headersStr) return null;
    
    try {
      const headers: Record<string, string> = {};
      const pairs = headersStr.split(',');
      
      for (const pair of pairs) {
        const [key, value] = pair.split('=', 2);
        if (key && value) {
          headers[key.trim()] = value.trim();
        } else {
          console.warn(`[FilinTracer] Failed to parse OTLP headers: ${headersStr}`);
          return null;
        }
      }
      
      return headers;
    } catch (error) {
      console.warn(`[FilinTracer] Failed to parse OTLP headers: ${headersStr}`, error);
      return null;
    }
  }
  
  /**
   * Парсит sampling ratio из переменной окружения
   */
  private static parseSamplingRatio(ratioStr?: string): number {
    if (!ratioStr) return 1.0;
    
    const ratio = parseFloat(ratioStr);
    if (isNaN(ratio) || ratio < 0 || ratio > 1) {
      console.warn(`[FilinTracer] Invalid sampling ratio: ${ratioStr}, using default: 1.0`);
      return 1.0;
    }
    
    return ratio;
  }
  
  /**
   * Валидирует конфигурацию
   */
  private static validateConfig(config: {
    autocompleteSlowMs: number;
    chatSlowMs: number;
    httpTimeoutMs: number;
    lspTimeoutMs: number;
    slowOperationMs: number;
    operationHangMs: number;
  }): void {
    const errors: string[] = [];
    
    // Проверяем что все значения положительные
    const fields = Object.entries(config);
    for (const [field, value] of fields) {
      if (value <= 0) {
        errors.push(`${field} must be positive, got: ${value}`);
      }
    }
    
    // Проверяем логические соотношения
    if (config.autocompleteSlowMs >= config.operationHangMs) {
      errors.push(`autocompleteSlowMs (${config.autocompleteSlowMs}) should be less than operationHangMs (${config.operationHangMs})`);
    }
    
    if (config.chatSlowMs >= config.operationHangMs) {
      errors.push(`chatSlowMs (${config.chatSlowMs}) should be less than operationHangMs (${config.operationHangMs})`);
    }
    
    if (config.httpTimeoutMs <= config.slowOperationMs) {
      errors.push(`httpTimeoutMs (${config.httpTimeoutMs}) should be greater than slowOperationMs (${config.slowOperationMs})`);
    }
    
    if (config.lspTimeoutMs <= config.slowOperationMs) {
      errors.push(`lspTimeoutMs (${config.lspTimeoutMs}) should be greater than slowOperationMs (${config.slowOperationMs})`);
    }
    
    if (errors.length > 0) {
      throw new ConfigValidationError(`Configuration validation failed: ${errors.join(', ')}`);
    }
  }
  
  /**
   * Сбрасывает кэшированную конфигурацию (для тестирования)
   */
  static resetCache(): void {
    ConfigLoader.cachedConfig = null;
  }
  
  /**
   * Проверяет включена ли трассировка
   */
  static isEnabled(): boolean {
    return ConfigLoader.loadConfig().enabled;
  }
  
  /**
   * Возвращает информацию о конфигурации для отладки
   */
  static getConfigInfo(): {
    isLoaded: boolean;
    configPaths: string[];
    enabled: boolean;
    level: string;
    isServer: boolean;
  } {
    return {
      isLoaded: ConfigLoader.cachedConfig !== null,
      configPaths: [], // В браузере нет файлов конфигурации
      enabled: ConfigLoader.cachedConfig?.enabled ?? false,
      level: ConfigLoader.cachedConfig?.level ?? 'INFO',
      isServer: false // Браузер никогда не сервер
    };
  }
}

// Для совместимости с filin-base экспортируем как ConfigLoaderBase
export { ConfigLoader as ConfigLoaderBase };