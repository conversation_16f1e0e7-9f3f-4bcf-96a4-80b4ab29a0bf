/**
 * Filin Tracer - Утилиты трассировки для Tabby Agent (LSP сервер)
 *
 * Принцип: Расширение базовых утилит для LSP-специфичных операций
 * Интеграция: Использует filin-base для общей функциональности с W3C Trace Context
 * Логирование: JSON Lines формат с интеграцией pino
 */

import {
  TraceUtilsBase,
  TraceConfig,
  TraceConfigManager,
  DataMasker,
  TraceEventTypes,
  ContextPropagation,
  TransportManager,
  TransportManagerConfig,
  CanonicalFields,
  type TraceEvent,
  type SpanContext,
  generateTraceId,
  generateSpanId,
  generateEventId
} from "filin-base";
import { AsyncLocalStorage } from "async_hooks";
// Browser-compatible crypto import
let createHash: any;
try {
  createHash = require("crypto").createHash;
} catch {
  // Fallback for browser environments
  createHash = null;
}

/**
 * LSP-специфичные типы операций (используем типы из filin-base)
 */
export const LSPOperationType = {
  LSP_SERVER_START: TraceEventTypes.LSP_REQUEST,
  LSP_SERVER_STOP: TraceEventTypes.LSP_RESPONSE,
  LSP_REQUEST_RECEIVED: TraceEventTypes.LSP_REQUEST,
  LSP_RESPONSE_SENT: TraceEventTypes.LSP_RESPONSE,
  LSP_NOTIFICATION_RECEIVED: TraceEventTypes.LSP_NOTIFICATION,
  HTTP_REQUEST_TO_SERVER: TraceEventTypes.HTTP_REQUEST,
  HTTP_RESPONSE_FROM_SERVER: TraceEventTypes.HTTP_RESPONSE,
  COMPLETION_REQUEST: TraceEventTypes.LSP_REQUEST,
  COMPLETION_RESPONSE: TraceEventTypes.LSP_RESPONSE,
  CHAT_REQUEST: TraceEventTypes.CHAT_REQUEST,
  CHAT_RESPONSE: TraceEventTypes.CHAT_RESPONSE,
} as const;

// Экспорт для обратной совместимости
export const LSPOp = LSPOperationType;

// Реэкспорт функций генерации ID из filin-base
export { generateTraceId, generateSpanId };

// Функция для извлечения trace_id из LSP параметров (для обратной совместимости)
export function extractTraceIdFromLspParams(params: any): string | null {
  const context = ContextPropagation.extractLSPParams(params);
  return context?.traceId || null;
}

export type LSPOperationTypeValue = (typeof LSPOperationType)[keyof typeof LSPOperationType];

/**
 * Интерфейс для HTTP запроса
 */
export interface HttpRequestInfo {
  method: string;
  url: string;
  headers?: Record<string, string>;
  body?: any;
}

/**
 * Контекст HTTP трассировки для упрощения вызовов
 */
export class HttpTraceContext {
  constructor(
    private tracer: LSPTraceUtils,
    public readonly traceId: string,
    public readonly spanId: string,
    public readonly idempotencyKey: string | undefined,
    public readonly requestInfo: HttpRequestInfo,
  ) { }

  /**
   * Логирует HTTP_REQUEST событие
   */
  logRequest(): void {
    this.tracer.httpRequestToServer(
      this.requestInfo,
      this.traceId,
      this.spanId,
      this.idempotencyKey,
    );
  }

  /**
   * Логирует HTTP_RESPONSE событие с успешным результатом
   */
  logResponse(duration: number, statusCode: number): void {
    this.tracer.httpResponseFromServer(
      this.requestInfo,
      this.traceId,
      this.spanId,
      duration,
      statusCode,
      true,
    );
  }

  /**
   * Логирует HTTP_RESPONSE событие с ошибкой
   */
  logError(duration: number): void {
    this.tracer.httpResponseFromServer(
      this.requestInfo,
      this.traceId,
      this.spanId,
      duration,
      0,
      false,
    );
  }
}

/**
 * Интерфейс для LSP сообщения
 */
export interface LSPMessageInfo {
  method: string;
  id?: string | number;
  params?: any;
}

/**
 * Утилиты трассировки для Tabby Agent (LSP сервер)
 * Расширяет базовые утилиты LSP-специфичными методами с поддержкой W3C Trace Context
 * Использует JSON Lines формат и интеграцию с pino
 */
export class LSPTraceUtils extends TraceUtilsBase {
  public config: TraceConfig;
  public dataMasker: DataMasker;
  private logger: any; // Pino logger для JSON Lines
  private contextStorage: AsyncLocalStorage<SpanContext>;
  private transportManager: TransportManager;

  private sessionIdentifiers: {
    sessionId: string;
    workspaceId: string;
    instanceId: string;
  };
  private sessionSequence: number = 0;

  constructor(config: TraceConfig, logger?: any, _transportConfig?: TransportManagerConfig) {
    super();
    this.config = config;
    this.dataMasker = new DataMasker(config);
    this.logger = logger || console;
    this.contextStorage = new AsyncLocalStorage<SpanContext>();

    this.sessionIdentifiers = ContextPropagation.createSessionIdentifiers();
    this.sessionSequence = 0;

    // Initialize a minimal transport manager for testing
    this.transportManager = {
      addTransport: () => { },
      removeTransport: () => { },
      send: async () => { },
      sendBatch: async () => { },
      close: async () => { }
    };
  }

  /**
   * Генерирует UUIDv7-подобный идентификатор (time-ordered)
   */
  private generateTimeOrderedUuid(): string {
    // Используем generateEventId из filin-base, который должен генерировать UUIDv7
    return generateEventId();
  }

  /**
   * Логирует событие трассировки в JSON Lines формате через транспорты
   */
  public logEvent(event: any): void {
    if (!this.config.enabled) return;

    const now = new Date();
    const fullEvent: TraceEvent = {
      [CanonicalFields.TIMESTAMP]: now.toISOString(),
      [CanonicalFields.TIMESTAMP_UNIX_NANOS]: (now.getTime() * 1000000).toString(),
      [CanonicalFields.EVENT_ID]: this.generateTimeOrderedUuid(),
      [CanonicalFields.LEVEL]: event.level || "INFO",
      [CanonicalFields.EVENT_VERSION]: "1.1.0",
      [CanonicalFields.SCHEMA_VERSION]: "filin.log.v1",
      [CanonicalFields.SERVICE_NAME]: "tabby-agent",
      [CanonicalFields.SERVICE_VERSION]: "1.9.0-dev",
      [CanonicalFields.SERVICE_INSTANCE_ID]: this.sessionIdentifiers.instanceId,
      [CanonicalFields.SERVICE_NAMESPACE]: "filin.agent",
      [CanonicalFields.DEPLOYMENT_ENVIRONMENT]: "dev",
      [CanonicalFields.COMPONENT]: event.component || "lsp-server",
      [CanonicalFields.IDE_SESSION_SEQ]: this.getNextSequence(),
      ...event,
    };

    // Отправляем через транспорты (асинхронно, не блокируем основной поток)
    this.transportManager.send(fullEvent).catch(error => {
      console.warn('Failed to send trace event:', error);
    });

    // Также логируем через существующий логгер для совместимости
    if (this.logger && typeof this.logger.info === "function") {
      this.logger.info(fullEvent);
    }
  }

  /**
   * Получает следующий номер последовательности (thread-safe)
   */
  private getNextSequence(): number {
    return ++this.sessionSequence;
  }

  /**
   * Генерирует Idempotency-Key для HTTP запросов
   */
  public generateIdempotencyKey(operation: string): string {
    return `${operation}-${this.generateTimeOrderedUuid()}`;
  }

  /**
   * Создает детерминированный хеш для корреляции без утечки оригинала
   */
  public hashValue(value: string): string {
    if (createHash) {
      const salt = process.env?.['FILIN_SALT'] || (process.env?.['FILIN_TEST_MODE'] === '1' ? 'test-salt' : '');
      return `sha256:${createHash('sha256')
        .update(value + salt)
        .digest('hex')
        .substring(0, 8)}...`;
    } else {
      // Fallback for browser environments - simple hash
      let hash = 0;
      for (let i = 0; i < value.length; i++) {
        const char = value.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // Convert to 32bit integer
      }
      return `hash:${Math.abs(hash).toString(16).substring(0, 8)}...`;
    }
  }

  /**
   * Создает новый span context с W3C Trace Context
   */
  public createSpanContext(operation: string, parent?: SpanContext): SpanContext {
    return ContextPropagation.createFilinContext(
      operation,
      "tabby-agent",
      this.sessionIdentifiers.sessionId,
      this.sessionIdentifiers.workspaceId,
      parent,
    );
  }

  /**
   * Получает текущий span context из AsyncLocalStorage
   */
  public getCurrentSpanContext(): SpanContext | undefined {
    return this.contextStorage.getStore();
  }

  /**
   * Выполняет операцию в контексте span
   */
  public runWithSpanContext<T>(context: SpanContext, fn: () => T): T {
    return this.contextStorage.run(context, fn);
  }

  /**
   * Выполняет асинхронную операцию в контексте span
   */
  public async runWithSpanContextAsync<T>(context: SpanContext, fn: () => Promise<T>): Promise<T> {
    return this.contextStorage.run(context, fn);
  }

  /**
   * Инжектирует W3C Trace Context в HTTP заголовки
   */
  public injectHttpHeaders(headers: Record<string, string>, context?: SpanContext): void {
    const spanContext = context || this.getCurrentSpanContext();
    if (spanContext) {
      ContextPropagation.injectHttpHeaders(headers, spanContext);
    }
  }

  /**
   * Извлекает W3C Trace Context из HTTP заголовков
   */
  public extractHttpHeaders(headers: Record<string, string>): SpanContext | null {
    return ContextPropagation.extractHttpHeaders(headers);
  }

  /**
   * Инжектирует W3C Trace Context в LSP параметры
   */
  public injectLSPParams(params: any, context?: SpanContext): any {
    const spanContext = context || this.getCurrentSpanContext();
    if (spanContext) {
      return ContextPropagation.injectLSPParams(params, spanContext);
    }
    return params;
  }

  /**
   * Извлекает W3C Trace Context из LSP параметров
   */
  public extractLSPParams(params: any): SpanContext | null {
    return ContextPropagation.extractLSPParams(params);
  }

  /**
   * Логирует запуск LSP сервера
   * @param serverInfo информация о сервере
   * @param traceId идентификатор трассировки (генерируется автоматически если не указан)
   * @return идентификатор трассировки
   */
  lspServerStart(serverInfo: any, traceId: string = generateTraceId()): string {
    if (!this.config.enabled) return traceId;

    const spanId = generateSpanId();
    this.logEvent({
      [CanonicalFields.EVENT]: "SPAN_START",
      [CanonicalFields.TRACE_ID]: traceId,
      [CanonicalFields.SPAN_ID]: spanId,
      [CanonicalFields.OPERATION]: "lsp_server_start",
      [CanonicalFields.SERVER_ADDRESS]: serverInfo?.name || "tabby-agent",
      [CanonicalFields.RUNTIME_VERSION]: process.version,
      [CanonicalFields.SESSION_ID]: this.sessionIdentifiers.sessionId,
    });
    return traceId;
  }

  /**
   * Логирует остановку LSP сервера
   * @param traceId идентификатор трассировки
   * @param duration длительность работы сервера в миллисекундах
   */
  lspServerStop(traceId: string, duration: number): void {
    if (!this.config.enabled) return;

    const spanId = generateSpanId();
    this.logEvent({
      [CanonicalFields.EVENT]: "SPAN_END",
      [CanonicalFields.TRACE_ID]: traceId,
      [CanonicalFields.SPAN_ID]: spanId,
      [CanonicalFields.OPERATION]: "lsp_server_start",
      [CanonicalFields.DURATION_MS]: Math.round(duration),
      [CanonicalFields.STOP_REASON]: "complete",
      [CanonicalFields.SESSION_ID]: this.sessionIdentifiers.sessionId,
    });
  }

  /**
   * Логирует получение LSP запроса
   * @param message информация о LSP сообщении
   * @param traceId идентификатор трассировки (генерируется автоматически если не указан)
   * @return идентификатор трассировки
   */
  lspRequestReceived(message: LSPMessageInfo, traceId: string = generateTraceId()): string {
    if (!this.config.enabled) return traceId;

    const spanId = generateSpanId();
    this.logEvent({
      [CanonicalFields.EVENT]: "LSP_REQUEST",
      [CanonicalFields.TRACE_ID]: traceId,
      [CanonicalFields.SPAN_ID]: spanId,
      [CanonicalFields.DIRECTION]: "received",
      [CanonicalFields.RPC_SYSTEM]: "jsonrpc",
      [CanonicalFields.RPC_METHOD]: message.method,
      [CanonicalFields.RPC_JSONRPC_REQUEST_ID]: message.id?.toString() || "none",
      [CanonicalFields.OPERATION]: message.method,
      [CanonicalFields.SESSION_ID]: this.sessionIdentifiers.sessionId,
    });
    return traceId;
  }

  /**
   * Логирует отправку LSP ответа
   * @param message информация о LSP сообщении
   * @param traceId идентификатор трассировки
   * @param duration длительность обработки в миллисекундах
   * @param success успешность обработки
   */
  lspResponseSent(message: LSPMessageInfo, traceId: string, duration: number, success: boolean = true): void {
    if (!this.config.enabled) return;

    const spanId = generateSpanId();

    // Проверяем на медленную операцию
    if (duration > this.config.lspTimeoutMs) {
      this.logEvent({
        [CanonicalFields.EVENT]: "LSP_TIMEOUT",
        [CanonicalFields.TRACE_ID]: traceId,
        [CanonicalFields.SPAN_ID]: spanId,
        [CanonicalFields.RPC_METHOD]: message.method,
        [CanonicalFields.RPC_JSONRPC_REQUEST_ID]: message.id?.toString() || "none",
        [CanonicalFields.DURATION_MS]: Math.round(duration),
        [CanonicalFields.STOP_REASON]: "deadline_exceeded",
        [CanonicalFields.SESSION_ID]: this.sessionIdentifiers.sessionId,
      });
    } else if (duration > this.config.autocompleteSlowMs && message.method.includes("completion")) {
      this.logEvent({
        [CanonicalFields.EVENT]: "SLOW_OPERATION",
        [CanonicalFields.TRACE_ID]: traceId,
        [CanonicalFields.SPAN_ID]: spanId,
        [CanonicalFields.SLOW_KIND]: "autocomplete",
        [CanonicalFields.RPC_METHOD]: message.method,
        [CanonicalFields.DURATION_MS]: Math.round(duration),
        [CanonicalFields.SESSION_ID]: this.sessionIdentifiers.sessionId,
      });
    }

    this.logEvent({
      [CanonicalFields.EVENT]: "LSP_RESPONSE",
      [CanonicalFields.TRACE_ID]: traceId,
      [CanonicalFields.SPAN_ID]: spanId,
      [CanonicalFields.DIRECTION]: "sent",
      [CanonicalFields.RPC_SYSTEM]: "jsonrpc",
      [CanonicalFields.RPC_METHOD]: message.method,
      [CanonicalFields.RPC_JSONRPC_REQUEST_ID]: message.id?.toString() || "none",
      [CanonicalFields.DURATION_MS]: Math.round(duration),
      [CanonicalFields.STOP_REASON]: success ? "complete" : "error",
      [CanonicalFields.SESSION_ID]: this.sessionIdentifiers.sessionId,
    });
  }

  /**
   * Логирует получение LSP уведомления
   * @param message информация о LSP сообщении
   * @param traceId идентификатор трассировки (генерируется автоматически если не указан)
   * @return идентификатор трассировки
   */
  lspNotificationReceived(message: LSPMessageInfo, traceId: string = generateTraceId()): string {
    if (!this.config.enabled) return traceId;

    const spanId = generateSpanId();
    this.logEvent({
      [CanonicalFields.EVENT]: "LSP_NOTIFICATION",
      [CanonicalFields.TRACE_ID]: traceId,
      [CanonicalFields.SPAN_ID]: spanId,
      [CanonicalFields.DIRECTION]: "received",
      [CanonicalFields.RPC_METHOD]: message.method,
      [CanonicalFields.OPERATION]: message.method,
      [CanonicalFields.SESSION_ID]: this.sessionIdentifiers.sessionId,
    });
    return traceId;
  }

  /**
   * Выполняет LSP операцию с автоматической трассировкой
   * Минимально инвазивная обертка для существующего кода с W3C Trace Context
   *
   * @param message информация о LSP сообщении
   * @param traceId идентификатор трассировки (опционально)
   * @param operation функция для выполнения
   * @return результат выполнения операции
   */
  async traceLspOperation<T>(
    message: LSPMessageInfo,
    traceId: string | null | undefined,
    operation: () => Promise<T>,
  ): Promise<T> {
    // Извлекаем или создаем span context
    let spanContext: SpanContext;

    if (traceId) {
      // Если передан traceId, создаем context на его основе
      const parentContext = this.getCurrentSpanContext();
      spanContext = this.createSpanContext(message.method, parentContext);
    } else {
      // Пытаемся извлечь context из LSP параметров
      const extractedContext = this.extractLSPParams(message.params);
      if (extractedContext) {
        spanContext = extractedContext;
      } else {
        // Создаем новый context
        spanContext = this.createSpanContext(message.method);
      }
    }

    const actualTraceId = spanContext.traceId;
    const startTime = Date.now();

    // Логируем начало операции
    this.lspRequestReceived(message, actualTraceId);

    return this.runWithSpanContextAsync(spanContext, async () => {
      try {
        const result = await operation();
        const duration = Date.now() - startTime;
        this.lspResponseSent(message, actualTraceId, duration, true);
        return result;
      } catch (error) {
        const duration = Date.now() - startTime;
        this.lspResponseSent(message, actualTraceId, duration, false);
        throw error;
      }
    });
  }

  /**
   * Логирует HTTP запрос к серверу (как span event)
   * @param request информация о HTTP запросе
   * @param traceId идентификатор трассировки (генерируется автоматически если не указан)
   * @param spanId идентификатор спана
   * @param idempotencyKey ключ идемпотентности
   * @return идентификатор трассировки
   */
  httpRequestToServer(request: HttpRequestInfo, traceId: string = generateTraceId(), spanId?: string, idempotencyKey?: string): string {
    if (!this.config.enabled) return traceId;

    // Маскируем чувствительные данные в URL и заголовках
    const maskedUrl = this.dataMasker.maskUrl(request.url);

    this.logEvent({
      [CanonicalFields.EVENT]: "HTTP_REQUEST",
      [CanonicalFields.TRACE_ID]: traceId,
      [CanonicalFields.SPAN_ID]: spanId || generateSpanId(),
      [CanonicalFields.HTTP_REQUEST_METHOD]: request.method,
      [CanonicalFields.URL_FULL]: maskedUrl,
      [CanonicalFields.REQUEST_IDEMPOTENCY_KEY]: idempotencyKey,
      [CanonicalFields.SESSION_ID]: this.sessionIdentifiers.sessionId,
    });
    return traceId;
  }

  /**
   * Логирует HTTP ответ от сервера (как span event) и завершение спана
   * @param request информация о HTTP запросе
   * @param traceId идентификатор трассировки
   * @param spanId идентификатор спана
   * @param duration длительность запроса в миллисекундах
   * @param statusCode HTTP статус код
   * @param success успешность запроса
   */
  httpResponseFromServer(
    request: HttpRequestInfo,
    traceId: string,
    spanId: string,
    duration: number,
    statusCode: number,
    success: boolean = true,
  ): void {
    if (!this.config.enabled) return;

    const maskedUrl = this.dataMasker.maskUrl(request.url);

    // Проверяем на таймаут или медленную операцию
    if (duration > this.config.httpTimeoutMs) {
      this.logEvent({
        [CanonicalFields.EVENT]: "HTTP_TIMEOUT",
        [CanonicalFields.TRACE_ID]: traceId,
        [CanonicalFields.SPAN_ID]: spanId,
        [CanonicalFields.HTTP_REQUEST_METHOD]: request.method,
        [CanonicalFields.URL_FULL]: maskedUrl,
        [CanonicalFields.DURATION_MS]: Math.round(duration),
        [CanonicalFields.STOP_REASON]: "deadline_exceeded",
        [CanonicalFields.SESSION_ID]: this.sessionIdentifiers.sessionId,
      });
    } else if (duration > 5000) { // Медленный HTTP запрос
      this.logEvent({
        [CanonicalFields.EVENT]: "SLOW_OPERATION",
        [CanonicalFields.TRACE_ID]: traceId,
        [CanonicalFields.SPAN_ID]: spanId,
        [CanonicalFields.SLOW_KIND]: "http",
        [CanonicalFields.HTTP_REQUEST_METHOD]: request.method,
        [CanonicalFields.URL_FULL]: maskedUrl,
        [CanonicalFields.DURATION_MS]: Math.round(duration),
        [CanonicalFields.SESSION_ID]: this.sessionIdentifiers.sessionId,
      });
    }

    // HTTP_RESPONSE как span event
    this.logEvent({
      [CanonicalFields.EVENT]: "HTTP_RESPONSE",
      [CanonicalFields.TRACE_ID]: traceId,
      [CanonicalFields.SPAN_ID]: spanId,
      [CanonicalFields.HTTP_REQUEST_METHOD]: request.method,
      [CanonicalFields.URL_FULL]: maskedUrl,
      [CanonicalFields.HTTP_RESPONSE_STATUS_CODE]: statusCode,
      [CanonicalFields.SESSION_ID]: this.sessionIdentifiers.sessionId,
    });

    // SPAN_END с длительностью
    this.logEvent({
      [CanonicalFields.EVENT]: "SPAN_END",
      [CanonicalFields.TRACE_ID]: traceId,
      [CanonicalFields.SPAN_ID]: spanId,
      [CanonicalFields.OPERATION]: `HTTP ${request.method}`,
      [CanonicalFields.DURATION_MS]: Math.round(duration),
      [CanonicalFields.STOP_REASON]: success ? "complete" : "error",
      [CanonicalFields.SESSION_ID]: this.sessionIdentifiers.sessionId,
    });
  }

  /**
   * Логирует запрос автодополнения
   * @param document информация о документе
   * @param position позиция в документе
   * @param traceId идентификатор трассировки (генерируется автоматически если не указан)
   * @return идентификатор трассировки
   */
  completionRequest(document: any, position: any, traceId: string = generateTraceId()): string {
    if (!this.config.enabled) return traceId;

    const spanId = generateSpanId();
    this.logEvent({
      [CanonicalFields.EVENT]: "SPAN_START",
      [CanonicalFields.TRACE_ID]: traceId,
      [CanonicalFields.SPAN_ID]: spanId,
      [CanonicalFields.OPERATION]: "completion_request",
      [CanonicalFields.DOCUMENT_LANGUAGE_ID]: document?.languageId || "unknown",
      [CanonicalFields.DOCUMENT_FILE_NAME]: document?.uri ? document.uri.split("/").pop() : "unknown",
      [CanonicalFields.DOCUMENT_POSITION_LINE]: position?.line || 0,
      [CanonicalFields.DOCUMENT_POSITION_CHARACTER]: position?.character || 0,
      [CanonicalFields.DOCUMENT_LENGTH]: document?.getText?.()?.length || 0,
      [CanonicalFields.SESSION_ID]: this.sessionIdentifiers.sessionId,
    });
    return traceId;
  }

  /**
   * Логирует ответ автодополнения
   * @param completions массив автодополнений
   * @param traceId идентификатор трассировки
   * @param duration длительность обработки в миллисекундах
   */
  completionResponse(completions: any[], traceId: string, duration: number): void {
    if (!this.config.enabled) return;

    const spanId = generateSpanId();

    // Проверяем на медленную операцию
    if (duration > this.config.autocompleteSlowMs) {
      this.logEvent({
        [CanonicalFields.EVENT]: "SLOW_OPERATION",
        [CanonicalFields.TRACE_ID]: traceId,
        [CanonicalFields.SPAN_ID]: spanId,
        [CanonicalFields.SLOW_KIND]: "autocomplete",
        [CanonicalFields.DURATION_MS]: Math.round(duration),
        [CanonicalFields.SESSION_ID]: this.sessionIdentifiers.sessionId,
      });
    }

    this.logEvent({
      [CanonicalFields.EVENT]: "SPAN_END",
      [CanonicalFields.TRACE_ID]: traceId,
      [CanonicalFields.SPAN_ID]: spanId,
      [CanonicalFields.OPERATION]: "completion_request",
      [CanonicalFields.DURATION_MS]: Math.round(duration),
      [CanonicalFields.COMPLETION_COUNT]: completions?.length || 0,
      [CanonicalFields.COMPLETION_HAS_RESULTS]: (completions?.length || 0) > 0,
      [CanonicalFields.STOP_REASON]: "complete",
      [CanonicalFields.SESSION_ID]: this.sessionIdentifiers.sessionId,
    });
  }

  /**
   * Логирует чат запрос
   * @param message сообщение чата
   * @param traceId идентификатор трассировки (генерируется автоматически если не указан)
   * @return идентификатор трассировки
   */
  chatRequest(message: string, traceId: string = generateTraceId()): string {
    if (!this.config.enabled) return traceId;

    const spanId = generateSpanId();

    this.logEvent({
      [CanonicalFields.EVENT]: "CHAT_MESSAGE_SENT",
      [CanonicalFields.TRACE_ID]: traceId,
      [CanonicalFields.SPAN_ID]: spanId,
      [CanonicalFields.OPERATION]: "chat_request",
      [CanonicalFields.CHAT_MESSAGE_LENGTH]: message.length,
      [CanonicalFields.CHAT_MESSAGE_HASH]: this.hashValue(message),
      [CanonicalFields.DATA_CLASSIFICATION]: "internal",
      [CanonicalFields.SESSION_ID]: this.sessionIdentifiers.sessionId,
    });
    return traceId;
  }

  /**
   * Логирует чат ответ
   * @param response ответ чата
   * @param traceId идентификатор трассировки
   * @param duration длительность обработки в миллисекундах
   */
  chatResponse(response: string, traceId: string, duration: number): void {
    if (!this.config.enabled) return;

    const spanId = generateSpanId();

    // Проверяем на медленную операцию
    if (duration > this.config.chatSlowMs) {
      this.logEvent({
        [CanonicalFields.EVENT]: "SLOW_OPERATION",
        [CanonicalFields.TRACE_ID]: traceId,
        [CanonicalFields.SPAN_ID]: spanId,
        [CanonicalFields.SLOW_KIND]: "chat",
        [CanonicalFields.DURATION_MS]: Math.round(duration),
        [CanonicalFields.SESSION_ID]: this.sessionIdentifiers.sessionId,
      });
    }

    this.logEvent({
      [CanonicalFields.EVENT]: "CHAT_RESPONSE",
      [CanonicalFields.TRACE_ID]: traceId,
      [CanonicalFields.SPAN_ID]: spanId,
      [CanonicalFields.OPERATION]: "chat_request",
      [CanonicalFields.DURATION_MS]: Math.round(duration),
      [CanonicalFields.CHAT_RESPONSE_LENGTH]: response.length,
      [CanonicalFields.CHAT_RESPONSE_HASH]: this.hashValue(response),
      [CanonicalFields.DATA_CLASSIFICATION]: "internal",
      [CanonicalFields.STOP_REASON]: "complete",
      [CanonicalFields.SESSION_ID]: this.sessionIdentifiers.sessionId,
    });
  }

  /**
   * Начинает трассировку операции (для обратной совместимости)
   * @param operation название операции
   * @param traceId идентификатор трассировки (генерируется автоматически если не указан)
   * @return идентификатор трассировки
   */
  startTrace(operation: string, traceId: string = generateTraceId()): string {
    if (!this.config.enabled) return traceId;

    const spanId = generateSpanId();
    this.logEvent({
      [CanonicalFields.EVENT]: "SPAN_START",
      [CanonicalFields.TRACE_ID]: traceId,
      [CanonicalFields.SPAN_ID]: spanId,
      [CanonicalFields.OPERATION]: operation,
      [CanonicalFields.SESSION_ID]: this.sessionIdentifiers.sessionId,
    });
    return traceId;
  }

  /**
   * Завершает трассировку операции (для обратной совместимости)
   * @param operation название операции
   * @param traceId идентификатор трассировки
   * @param duration длительность операции в миллисекундах
   */
  finishTrace(operation: string, traceId: string, duration: number): void {
    if (!this.config.enabled) return;

    const spanId = generateSpanId();

    if (duration > this.config.slowOperationMs) {
      this.logEvent({
        [CanonicalFields.EVENT]: "SLOW_OPERATION",
        [CanonicalFields.TRACE_ID]: traceId,
        [CanonicalFields.SPAN_ID]: spanId,
        [CanonicalFields.SLOW_KIND]: "unknown",
        [CanonicalFields.DURATION_MS]: Math.round(duration),
        [CanonicalFields.SESSION_ID]: this.sessionIdentifiers.sessionId,
      });
    }

    this.logEvent({
      [CanonicalFields.EVENT]: "SPAN_END",
      [CanonicalFields.TRACE_ID]: traceId,
      [CanonicalFields.SPAN_ID]: spanId,
      [CanonicalFields.OPERATION]: operation,
      [CanonicalFields.DURATION_MS]: Math.round(duration),
      [CanonicalFields.STOP_REASON]: "complete",
      [CanonicalFields.SESSION_ID]: this.sessionIdentifiers.sessionId,
    });
  }

  /**
   * Логирует ошибку операции (для обратной совместимости)
   * @param operation название операции
   * @param traceId идентификатор трассировки
   * @param error описание ошибки
   */
  errorTrace(operation: string, traceId: string, error: string): void {
    if (!this.config.enabled) return;

    const maskedError = this.dataMasker.maskSensitiveData(error);
    const spanId = generateSpanId();

    this.logEvent({
      [CanonicalFields.EVENT]: "SPAN_END",
      [CanonicalFields.TRACE_ID]: traceId,
      [CanonicalFields.SPAN_ID]: spanId,
      [CanonicalFields.OPERATION]: operation,
      [CanonicalFields.DURATION_MS]: 0, // Error occurred immediately
      [CanonicalFields.STOP_REASON]: "error",
      [CanonicalFields.ERROR_TYPE]: "Error",
      [CanonicalFields.ERROR_MESSAGE]: maskedError.maskedData,
      [CanonicalFields.SESSION_ID]: this.sessionIdentifiers.sessionId,
    });
  }

  /**
   * Завершает LSP трассировку (для обратной совместимости)
   * @param operation название операции
   * @param method LSP метод
   * @param traceId идентификатор трассировки
   * @param duration длительность операции в миллисекундах
   */
  finishLspTrace(operation: string, method: string, traceId: string, duration: number): void {
    if (!this.config.enabled) return;

    const spanId = generateSpanId();

    // Проверяем на медленную операцию
    if (duration > this.config.lspTimeoutMs) {
      this.logEvent({
        [CanonicalFields.EVENT]: "LSP_TIMEOUT",
        [CanonicalFields.TRACE_ID]: traceId,
        [CanonicalFields.SPAN_ID]: spanId,
        [CanonicalFields.RPC_METHOD]: method,
        [CanonicalFields.DURATION_MS]: Math.round(duration),
        [CanonicalFields.STOP_REASON]: "deadline_exceeded",
        [CanonicalFields.SESSION_ID]: this.sessionIdentifiers.sessionId,
      });
    } else if (duration > this.config.autocompleteSlowMs && method.includes("completion")) {
      this.logEvent({
        [CanonicalFields.EVENT]: "SLOW_OPERATION",
        [CanonicalFields.TRACE_ID]: traceId,
        [CanonicalFields.SPAN_ID]: spanId,
        [CanonicalFields.SLOW_KIND]: "autocomplete",
        [CanonicalFields.RPC_METHOD]: method,
        [CanonicalFields.DURATION_MS]: Math.round(duration),
        [CanonicalFields.SESSION_ID]: this.sessionIdentifiers.sessionId,
      });
    }

    this.logEvent({
      [CanonicalFields.EVENT]: "SPAN_END",
      [CanonicalFields.TRACE_ID]: traceId,
      [CanonicalFields.SPAN_ID]: spanId,
      [CanonicalFields.OPERATION]: operation,
      [CanonicalFields.RPC_METHOD]: method,
      [CanonicalFields.DURATION_MS]: Math.round(duration),
      [CanonicalFields.STOP_REASON]: "complete",
      [CanonicalFields.SESSION_ID]: this.sessionIdentifiers.sessionId,
    });
  }

  /**
   * Выполняет синхронную функцию с автоматической трассировкой (для обратной совместимости)
   * @param operation название операции для трассировки
   * @param fn функция для выполнения
   * @return результат выполнения функции
   */
  trace<T>(operation: string, fn: () => T): T {
    const traceId = this.startTrace(operation);
    const startTime = Date.now();

    try {
      const result = fn();
      this.finishTrace(operation, traceId, Date.now() - startTime);
      return result;
    } catch (error) {
      this.errorTrace(operation, traceId, error instanceof Error ? error.message : String(error));
      throw error;
    }
  }

  /**
   * Выполняет асинхронную функцию с автоматической трассировкой (для обратной совместимости)
   * @param operation название операции для трассировки
   * @param fn асинхронная функция для выполнения
   * @return результат выполнения функции
   */
  async traceAsync<T>(operation: string, fn: () => Promise<T>): Promise<T> {
    const traceId = this.startTrace(operation);
    const startTime = Date.now();

    try {
      const result = await fn();
      this.finishTrace(operation, traceId, Date.now() - startTime);
      return result;
    } catch (error) {
      this.errorTrace(operation, traceId, error instanceof Error ? error.message : String(error));
      throw error;
    }
  }

  /**
   * Закрывает все транспорты и освобождает ресурсы
   */
  async close(): Promise<void> {
    await this.transportManager.close();
  }

  /**
   * Получает список активных транспортов
   * Note: This method is deprecated, use NodeJSAdapter instead
   */
  getActiveTransports(): string[] {
    return [];
  }

  /**
   * Создает HTTP контекст трассировки для упрощения вызовов
   * Автоматически создает Idempotency-Key и добавляет его в заголовки
   */
  createHttpTraceContext(
    method: string,
    url: string,
    body?: any,
    operationName?: string,
    headers: Record<string, string> = {},
  ): HttpTraceContext {
    const traceId = generateTraceId();
    const spanId = generateSpanId();
    const idempotencyKey = this.generateIdempotencyKey(operationName || method.toLowerCase());

    // Создаем заголовки с Idempotency-Key
    const finalHeaders = { ...headers };
    if (idempotencyKey && (method === "POST" || method === "PUT")) {
      finalHeaders["Idempotency-Key"] = idempotencyKey;
    }

    const requestInfo: HttpRequestInfo = {
      method,
      url,
      headers: finalHeaders,
      body,
    };

    // Логируем SPAN_START
    this.logEvent({
      [CanonicalFields.EVENT]: "SPAN_START",
      [CanonicalFields.TRACE_ID]: traceId,
      [CanonicalFields.SPAN_ID]: spanId,
      [CanonicalFields.OPERATION]: operationName || `HTTP ${method}`,
      [CanonicalFields.HTTP_REQUEST_METHOD]: method,
      [CanonicalFields.URL_FULL]: url,
      [CanonicalFields.REQUEST_IDEMPOTENCY_KEY]: idempotencyKey,
    });

    return new HttpTraceContext(this, traceId, spanId, idempotencyKey, requestInfo);
  }

  /**
   * Создает экземпляр трассировщика с конфигурацией по умолчанию
   * @param logger логгер tabby-agent (pino)
   * @param transportConfig конфигурация транспортов (опционально)
   * @return экземпляр LSPTraceUtils
   */
  static createDefault(logger?: any, transportConfig?: TransportManagerConfig): LSPTraceUtils {
    // Загружаем конфигурацию из filin-base с переопределениями для tabby-agent
    const baseConfig = TraceConfigManager.loadDefaultConfig();

    const config: TraceConfig = {
      ...baseConfig,
      // Специфичные для tabby-agent настройки
      autocompleteSlowMs: parseInt(process.env['FILIN_AUTOCOMPLETE_SLOW_MS'] || '1000'),
      chatSlowMs: parseInt(process.env['FILIN_CHAT_SLOW_MS'] || '3000'),
      httpTimeoutMs: parseInt(process.env['FILIN_HTTP_TIMEOUT_MS'] || '20000'),
      lspTimeoutMs: parseInt(process.env['FILIN_LSP_TIMEOUT_MS'] || '30000'),
      testMode: process.env['FILIN_TEST_MODE'] === '1',
      streamFullLog: process.env['FILIN_STREAM_FULL_LOG'] === '1',
    };

    return new LSPTraceUtils(config, logger, transportConfig);
  }

  // Методы для обратной совместимости с tabbyApiClient

  /**
   * Начинает HTTP запрос (для обратной совместимости)
   * @param operation название операции
   * @param method HTTP метод
   * @param path путь запроса
   * @return trace_id
   */
  startHttpRequest(_operation: string, method: string, path: string): string {
    if (!this.config.enabled) return "";

    const parentContext = this.getCurrentSpanContext();
    const spanContext = this.createSpanContext(`${method} ${path}`, parentContext);
    const idempotencyKey = this.generateIdempotencyKey(`http-${method.toLowerCase()}`);

    const requestInfo: HttpRequestInfo = {
      method,
      url: path,
      headers: {},
    };

    this.httpRequestToServer(requestInfo, spanContext.traceId, spanContext.spanId, idempotencyKey);
    return spanContext.traceId;
  }

  /**
   * Логирует успешный HTTP ответ (для обратной совместимости)
   * @param operation название операции
   * @param method HTTP метод
   * @param path путь запроса
   * @param traceId идентификатор трассировки
   * @param statusCode статус код
   * @param duration длительность в миллисекундах
   */
  traceHttpSuccess(
    _operation: string,
    method: string,
    path: string,
    traceId: string,
    statusCode: number,
    duration: number,
  ): void {
    if (!this.config.enabled) return;

    const requestInfo: HttpRequestInfo = {
      method,
      url: path,
      headers: {},
    };

    const spanId = generateSpanId(); // Генерируем новый spanId для совместимости
    this.httpResponseFromServer(requestInfo, traceId, spanId, duration, statusCode, true);
  }

  /**
   * Логирует ошибку HTTP запроса (для обратной совместимости)
   * @param operation название операции
   * @param method HTTP метод
   * @param path путь запроса
   * @param traceId идентификатор трассировки
   * @param error ошибка
   */
  traceHttpError(_operation: string, method: string, path: string, traceId: string, error: any): void {
    if (!this.config.enabled) return;

    const requestInfo: HttpRequestInfo = {
      method,
      url: path,
      headers: {},
    };

    const spanId = generateSpanId(); // Генерируем новый spanId для совместимости
    const errorMessage = error instanceof Error ? error.message : String(error);

    // Логируем HTTP_ERROR
    this.logEvent({
      [CanonicalFields.EVENT]: "HTTP_ERROR",
      [CanonicalFields.TRACE_ID]: traceId,
      [CanonicalFields.SPAN_ID]: spanId,
      [CanonicalFields.HTTP_REQUEST_METHOD]: method,
      [CanonicalFields.URL_FULL]: this.dataMasker.maskUrl(path),
      [CanonicalFields.ERROR_TYPE]: error?.constructor?.name || "Error",
      [CanonicalFields.ERROR_MESSAGE]: errorMessage,
      [CanonicalFields.STOP_REASON]: "error",
      [CanonicalFields.SESSION_ID]: this.sessionIdentifiers.sessionId,
    });

    this.httpResponseFromServer(requestInfo, traceId, spanId, 0, 0, false);
  }
}

// Экспорт для обратной совместимости
export { LSPTraceUtils as TraceUtils };
