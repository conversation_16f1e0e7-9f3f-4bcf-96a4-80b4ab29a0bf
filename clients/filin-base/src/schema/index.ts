/**
 * JSON Schema validation for filin-base
 * 
 * Provides comprehensive JSON Schema validation for trace events with canonical field naming,
 * multiple validation modes, detailed error reporting, and metrics collection.
 */

// Legacy validator (deprecated - use UniversalValidator instead)
export * from './EventValidator';
export * from './SchemaTypes';
export * from './ValidationUtils';

// New universal validation system
export { UniversalValidator, ValidationMode, ValidationResult, ValidationContext, ValidationErrorType, ValidationError, ValidationWarning, ValidationMetrics } from './UniversalValidator';
export { FILIN_SCHEMA_ID, FILIN_SCHEMA_VERSION, SCHEMA_CONFIG } from './SchemaConstants';
export * from './ValidationErrorHandler';
export * from './ValidationService';
export * from './ValidationUtilities';