/**
 * Канонические типы событий трассировки для tabby-chat-panel
 * 
 * Использует точечную нотацию согласно OpenTelemetry стандартам
 * и каноническую схему именования полей filin.log.v1
 * 
 * Фокус на GenAI полях для чат-функциональности
 */

import { CanonicalFields } from "filin-base/browser";

/**
 * Каноническое событие трассировки с точечной нотацией полей
 */
export interface CanonicalTraceEvent {
  // Временные поля
  [CanonicalFields.TIMESTAMP]: string;                    // ISO-8601 UTC timestamp with Z
  [CanonicalFields.TIMESTAMP_UNIX_NANOS]: string;         // Nanoseconds since epoch as string

  // Идентификаторы  
  [CanonicalFields.EVENT_ID]: string;              // UUIDv7 for time-ordered events
  [CanonicalFields.TRACE_ID]: string;              // W3C Trace Context trace ID (32 hex lower-case)
  [CanonicalFields.SPAN_ID]: string;               // W3C Trace Context span ID (16 hex lower-case)
  [CanonicalFields.PARENT_SPAN_ID]?: string;       // Parent span ID (16 hex lower-case, optional)

  // W3C Trace Context
  [CanonicalFields.TRACE_FLAGS]?: string;          // W3C Trace Context flags (2 hex)
  [CanonicalFields.TRACE_STATE]?: string;          // W3C Trace Context state (≤512 bytes)

  // Метаданные
  [CanonicalFields.LEVEL]: 'DEBUG' | 'INFO' | 'WARN' | 'ERROR';
  [CanonicalFields.EVENT]: CanonicalEventType;
  [CanonicalFields.EVENT_VERSION]: string;
  [CanonicalFields.SCHEMA_VERSION]: string;

  // Ресурсы (OpenTelemetry)
  [CanonicalFields.SERVICE_NAME]: string;
  [CanonicalFields.SERVICE_VERSION]: string;
  [CanonicalFields.SERVICE_INSTANCE_ID]: string;
  [CanonicalFields.SERVICE_NAMESPACE]: string;
  [CanonicalFields.DEPLOYMENT_ENVIRONMENT]: string;
  [CanonicalFields.COMPONENT]: string;
  [CanonicalFields.OPERATION]?: string;
  [CanonicalFields.DIRECTION]?: 'sent' | 'received';

  // Семантические поля
  [CanonicalFields.DURATION_MS]?: number;
  [CanonicalFields.STOP_REASON]?: 'complete' | 'canceled_by_user' | 'deadline_exceeded' | 'error';
  [CanonicalFields.DATA_CLASSIFICATION]?: 'public' | 'internal' | 'confidential' | 'secret';

  // HTTP поля (OpenTelemetry Semantic Conventions)
  [CanonicalFields.HTTP_REQUEST_METHOD]?: string;
  [CanonicalFields.URL_PATH]?: string;
  [CanonicalFields.URL_QUERY]?: string;
  [CanonicalFields.URL_FULL]?: string;
  [CanonicalFields.HTTP_RESPONSE_STATUS_CODE]?: number;
  [CanonicalFields.HTTP_ROUTE]?: string;
  [CanonicalFields.USER_AGENT_ORIGINAL]?: string;
  [CanonicalFields.SERVER_ADDRESS]?: string;
  [CanonicalFields.SERVER_PORT]?: number;
  [CanonicalFields.CLIENT_ADDRESS]?: string;
  [CanonicalFields.NETWORK_PEER_ADDRESS]?: string;
  [CanonicalFields.NETWORK_PEER_PORT]?: number;

  // JSON-RPC (LSP) поля
  [CanonicalFields.RPC_SYSTEM]?: 'jsonrpc';
  [CanonicalFields.RPC_METHOD]?: string;
  [CanonicalFields.RPC_JSONRPC_REQUEST_ID]?: string;
  [CanonicalFields.RPC_JSONRPC_PARAMS_BYTES]?: number;
  [CanonicalFields.RPC_JSONRPC_RESULT_BYTES]?: number;

  // GenAI (Chat/LLM) поля - основной фокус для tabby-chat-panel
  [CanonicalFields.GEN_AI_PROVIDER_NAME]?: string;
  [CanonicalFields.GEN_AI_REQUEST_MODEL]?: string;
  [CanonicalFields.GEN_AI_USAGE_INPUT_TOKENS]?: number;
  [CanonicalFields.GEN_AI_USAGE_OUTPUT_TOKENS]?: number;

  // Ошибки
  [CanonicalFields.ERROR_TYPE]?: string;
  [CanonicalFields.ERROR_MESSAGE]?: string;
  [CanonicalFields.ERROR_STACK]?: string;
  [CanonicalFields.ERROR_STACK_TRUNCATED]?: boolean;

  // Стриминг
  [CanonicalFields.STREAM_ID]?: string;
  [CanonicalFields.STREAM_SEQ]?: number;
  [CanonicalFields.STREAM_CHUNKS_TOTAL]?: number;
  [CanonicalFields.BYTES_TOTAL]?: number;
  [CanonicalFields.TIME_TO_FIRST_TOKEN]?: number;
  [CanonicalFields.TIME_TO_LAST_BYTE]?: number;
  [CanonicalFields.CHUNK_INDEX]?: number;

  // Диагностика
  [CanonicalFields.SLOW_KIND]?: string;
  [CanonicalFields.HANG_KIND]?: string;

  // Сессия и контекст
  [CanonicalFields.SESSION_ID]?: string;
  [CanonicalFields.IDE_NAME]?: string;
  [CanonicalFields.IDE_VERSION]?: string;
  [CanonicalFields.PLUGIN_VERSION]?: string;
  [CanonicalFields.OS]?: string;
  [CanonicalFields.WORKSPACE_ID]?: string;
  [CanonicalFields.REPO_HASH]?: string;
  [CanonicalFields.USER_ANONYMOUS_ID]?: string;
  [CanonicalFields.IDE_SESSION_SEQ]?: number;
  [CanonicalFields.REQUEST_IDEMPOTENCY_KEY]?: string;

  // WebSocket специфичные поля
  [CanonicalFields.WEBSOCKET_CONNECTION_ID]?: string;
  [CanonicalFields.WEBSOCKET_URL]?: string;
  [CanonicalFields.WEBSOCKET_PROTOCOLS]?: string;
  [CanonicalFields.WEBSOCKET_DIRECTION]?: 'sent' | 'received';
  [CanonicalFields.WEBSOCKET_MESSAGE_TYPE]?: 'text' | 'binary' | 'ping' | 'pong' | 'close';
  [CanonicalFields.WEBSOCKET_MESSAGE_SIZE]?: number;
  [CanonicalFields.WEBSOCKET_CLOSE_CODE]?: number;
  [CanonicalFields.WEBSOCKET_CLOSE_REASON]?: string;

  // Чат специфичные поля
  [CanonicalFields.MESSAGE_ID]?: string;
  [CanonicalFields.MESSAGE_ROLE]?: 'user' | 'assistant';
  [CanonicalFields.MESSAGE_CONTENT_LENGTH]?: number;
  [CanonicalFields.MESSAGE_HASH]?: string;
  [CanonicalFields.CHAT_COMMAND_TYPE]?: string;
  [CanonicalFields.CONTEXT_HAS_CONTENT]?: boolean;

  // Smart Apply поля
  [CanonicalFields.SMART_APPLY_CHANGES_COUNT]?: number;
  [CanonicalFields.SMART_APPLY_HAS_CHANGES]?: boolean;
  [CanonicalFields.SMART_APPLY_SUCCESS]?: boolean;

  // Commit Message поля
  [CanonicalFields.COMMIT_CHANGES_COUNT]?: number;
  [CanonicalFields.COMMIT_HAS_CHANGES]?: boolean;
  [CanonicalFields.COMMIT_MESSAGE_LENGTH]?: number;
  [CanonicalFields.COMMIT_MESSAGE_HASH]?: string;
  [CanonicalFields.COMMIT_SUCCESS]?: boolean;

  // Кастомные атрибуты (должны соответствовать паттерну attributes.*)
  [key: `attributes.${string}`]: string | number | boolean;
}

/**
 * Канонические типы событий для чат-панели
 */
export type CanonicalEventType = 
  | 'SPAN_START'
  | 'SPAN_END'
  | 'HTTP_REQUEST'
  | 'HTTP_RESPONSE'
  | 'HTTP_ERROR'
  | 'HTTP_TIMEOUT'
  | 'CHAT_MESSAGE_SENT'
  | 'CHAT_REQUEST'
  | 'CHAT_RESPONSE'
  | 'CHAT_STREAM_START'
  | 'CHAT_STREAM_CHUNK'
  | 'CHAT_STREAM_END'
  | 'WEBSOCKET_CONNECTION_START'
  | 'WEBSOCKET_MESSAGE'
  | 'WEBSOCKET_CONNECTION_END'
  | 'SLOW_OPERATION'
  | 'HANG_DETECTED';

/**
 * Информация о GenAI запросе с каноническими полями
 */
export interface CanonicalGenAIInfo {
  [CanonicalFields.GEN_AI_PROVIDER_NAME]?: string;
  [CanonicalFields.GEN_AI_REQUEST_MODEL]?: string;
  [CanonicalFields.GEN_AI_USAGE_INPUT_TOKENS]?: number;
  [CanonicalFields.GEN_AI_USAGE_OUTPUT_TOKENS]?: number;
}

/**
 * Информация о чат-сообщении с каноническими полями
 */
export interface CanonicalChatMessageInfo {
  [CanonicalFields.MESSAGE_ID]: string;
  [CanonicalFields.MESSAGE_ROLE]: 'user' | 'assistant';
  [CanonicalFields.MESSAGE_CONTENT_LENGTH]: number;
  [CanonicalFields.MESSAGE_HASH]?: string;
}

/**
 * Информация о стриминге с каноническими полями
 */
export interface CanonicalStreamInfo {
  [CanonicalFields.STREAM_ID]: string;
  [CanonicalFields.STREAM_SEQ]?: number;
  [CanonicalFields.STREAM_CHUNKS_TOTAL]?: number;
  [CanonicalFields.BYTES_TOTAL]?: number;
  [CanonicalFields.TIME_TO_FIRST_TOKEN]?: number;
  [CanonicalFields.TIME_TO_LAST_BYTE]?: number;
  [CanonicalFields.CHUNK_INDEX]?: number;
}

/**
 * Информация о WebSocket с каноническими полями
 */
export interface CanonicalWebSocketInfo {
  [CanonicalFields.WEBSOCKET_CONNECTION_ID]: string;
  [CanonicalFields.WEBSOCKET_URL]: string;
  [CanonicalFields.WEBSOCKET_PROTOCOLS]?: string;
  [CanonicalFields.WEBSOCKET_DIRECTION]?: 'sent' | 'received';
  [CanonicalFields.WEBSOCKET_MESSAGE_TYPE]?: 'text' | 'binary' | 'ping' | 'pong' | 'close';
  [CanonicalFields.WEBSOCKET_MESSAGE_SIZE]?: number;
  [CanonicalFields.WEBSOCKET_CLOSE_CODE]?: number;
  [CanonicalFields.WEBSOCKET_CLOSE_REASON]?: string;
}

/**
 * Таблица миграции старых имен полей на канонические для tabby-chat-panel
 */
export const FIELD_MIGRATION_TABLE: Record<string, string> = {
  // Идентификаторы и временные метки
  'traceId': CanonicalFields.TRACE_ID,
  'trace_id': CanonicalFields.TRACE_ID,
  'spanId': CanonicalFields.SPAN_ID,
  'span_id': CanonicalFields.SPAN_ID,
  'parentSpanId': CanonicalFields.PARENT_SPAN_ID,
  'parent_span_id': CanonicalFields.PARENT_SPAN_ID,
  'eventId': CanonicalFields.EVENT_ID,
  'event_id': CanonicalFields.EVENT_ID,
  'timestamp': CanonicalFields.TIMESTAMP,
  'ts_unix_nanos': CanonicalFields.TIMESTAMP_UNIX_NANOS,
  'startTimeUnixNano': CanonicalFields.TIMESTAMP_UNIX_NANOS,
  'endTimeUnixNano': CanonicalFields.TIMESTAMP_UNIX_NANOS,
  'duration': CanonicalFields.DURATION_MS,
  'duration_ms': CanonicalFields.DURATION_MS,

  // HTTP поля (OpenTelemetry Semantic Conventions)
  'http.method': CanonicalFields.HTTP_REQUEST_METHOD,
  'httpMethod': CanonicalFields.HTTP_REQUEST_METHOD,
  'http.target': CanonicalFields.URL_PATH,
  'httpTarget': CanonicalFields.URL_PATH,
  'http.status.code': CanonicalFields.HTTP_RESPONSE_STATUS_CODE,
  'httpStatusCode': CanonicalFields.HTTP_RESPONSE_STATUS_CODE,
  'http.route': CanonicalFields.HTTP_ROUTE,
  'httpRoute': CanonicalFields.HTTP_ROUTE,
  'url': CanonicalFields.URL_FULL,
  'fullUrl': CanonicalFields.URL_FULL,
  'userAgent': CanonicalFields.USER_AGENT_ORIGINAL,
  'user_agent': CanonicalFields.USER_AGENT_ORIGINAL,
  'serverAddress': CanonicalFields.SERVER_ADDRESS,
  'server_address': CanonicalFields.SERVER_ADDRESS,
  'serverPort': CanonicalFields.SERVER_PORT,
  'server_port': CanonicalFields.SERVER_PORT,
  'clientAddress': CanonicalFields.CLIENT_ADDRESS,
  'client_address': CanonicalFields.CLIENT_ADDRESS,
  'net.peer.name': CanonicalFields.NETWORK_PEER_ADDRESS,
  'netPeerName': CanonicalFields.NETWORK_PEER_ADDRESS,
  'net.peer.port': CanonicalFields.NETWORK_PEER_PORT,
  'netPeerPort': CanonicalFields.NETWORK_PEER_PORT,

  // GenAI поля (GenAI Semantic Conventions) - основной фокус для tabby-chat-panel
  'model.name': CanonicalFields.GEN_AI_REQUEST_MODEL,
  'modelName': CanonicalFields.GEN_AI_REQUEST_MODEL,
  'prompt.tokens': CanonicalFields.GEN_AI_USAGE_INPUT_TOKENS,
  'promptTokens': CanonicalFields.GEN_AI_USAGE_INPUT_TOKENS,
  'completion.tokens': CanonicalFields.GEN_AI_USAGE_OUTPUT_TOKENS,
  'completionTokens': CanonicalFields.GEN_AI_USAGE_OUTPUT_TOKENS,

  // Ошибки и диагностика
  'error.type': CanonicalFields.ERROR_TYPE,
  'errorType': CanonicalFields.ERROR_TYPE,
  'error.message': CanonicalFields.ERROR_MESSAGE,
  'errorMessage': CanonicalFields.ERROR_MESSAGE,
  'error.stack': CanonicalFields.ERROR_STACK,
  'errorStack': CanonicalFields.ERROR_STACK,
  'slow.kind': CanonicalFields.SLOW_KIND,
  'slowKind': CanonicalFields.SLOW_KIND,
  'hang.kind': CanonicalFields.HANG_KIND,
  'hangKind': CanonicalFields.HANG_KIND,

  // Стриминг поля
  'stream_id': CanonicalFields.STREAM_ID,
  'streamId': CanonicalFields.STREAM_ID,
  'stream_seq': CanonicalFields.STREAM_SEQ,
  'streamSeq': CanonicalFields.STREAM_SEQ,
  'chunks_total': CanonicalFields.STREAM_CHUNKS_TOTAL,
  'chunksTotal': CanonicalFields.STREAM_CHUNKS_TOTAL,
  'bytes_total': CanonicalFields.BYTES_TOTAL,
  'bytesTotal': CanonicalFields.BYTES_TOTAL,
  'time_to_first_token': CanonicalFields.TIME_TO_FIRST_TOKEN,
  'timeToFirstToken': CanonicalFields.TIME_TO_FIRST_TOKEN,
  'time_to_last_byte': CanonicalFields.TIME_TO_LAST_BYTE,
  'timeToLastByte': CanonicalFields.TIME_TO_LAST_BYTE,
  'chunk_index': CanonicalFields.CHUNK_INDEX,
  'chunkIndex': CanonicalFields.CHUNK_INDEX,

  // WebSocket поля
  'websocket_connection_id': CanonicalFields.WEBSOCKET_CONNECTION_ID,
  'websocketConnectionId': CanonicalFields.WEBSOCKET_CONNECTION_ID,
  'websocket_url': CanonicalFields.WEBSOCKET_URL,
  'websocketUrl': CanonicalFields.WEBSOCKET_URL,
  'websocket_protocols': CanonicalFields.WEBSOCKET_PROTOCOLS,
  'websocketProtocols': CanonicalFields.WEBSOCKET_PROTOCOLS,
  'websocket_direction': CanonicalFields.WEBSOCKET_DIRECTION,
  'websocketDirection': CanonicalFields.WEBSOCKET_DIRECTION,
  'websocket_message_type': CanonicalFields.WEBSOCKET_MESSAGE_TYPE,
  'websocketMessageType': CanonicalFields.WEBSOCKET_MESSAGE_TYPE,
  'websocket_message_size': CanonicalFields.WEBSOCKET_MESSAGE_SIZE,
  'websocketMessageSize': CanonicalFields.WEBSOCKET_MESSAGE_SIZE,
  'websocket_close_code': CanonicalFields.WEBSOCKET_CLOSE_CODE,
  'websocketCloseCode': CanonicalFields.WEBSOCKET_CLOSE_CODE,
  'websocket_close_reason': CanonicalFields.WEBSOCKET_CLOSE_REASON,
  'websocketCloseReason': CanonicalFields.WEBSOCKET_CLOSE_REASON,

  // Чат поля
  'message_id': CanonicalFields.MESSAGE_ID,
  'messageId': CanonicalFields.MESSAGE_ID,
  'message_role': CanonicalFields.MESSAGE_ROLE,
  'messageRole': CanonicalFields.MESSAGE_ROLE,
  'message_content_length': CanonicalFields.MESSAGE_CONTENT_LENGTH,
  'messageContentLength': CanonicalFields.MESSAGE_CONTENT_LENGTH,
  'message_hash': CanonicalFields.MESSAGE_HASH,
  'messageHash': CanonicalFields.MESSAGE_HASH,
  'chat_command_type': CanonicalFields.CHAT_COMMAND_TYPE,
  'chatCommandType': CanonicalFields.CHAT_COMMAND_TYPE,
  'context_has_content': CanonicalFields.CONTEXT_HAS_CONTENT,
  'contextHasContent': CanonicalFields.CONTEXT_HAS_CONTENT
};

/**
 * Утилита для миграции старых полей на канонические имена
 */
export function migrateFieldNames(event: Record<string, any>): CanonicalTraceEvent {
  const migratedEvent: Record<string, any> = {};

  for (const [key, value] of Object.entries(event)) {
    const canonicalKey = FIELD_MIGRATION_TABLE[key] || key;
    migratedEvent[canonicalKey] = value;
  }

  return migratedEvent as CanonicalTraceEvent;
}

/**
 * Список канонических полей из схемы
 */
const CANONICAL_FIELDS = new Set([
  CanonicalFields.TIMESTAMP, CanonicalFields.TIMESTAMP_UNIX_NANOS, CanonicalFields.EVENT_ID, 
  CanonicalFields.TRACE_ID, CanonicalFields.SPAN_ID, CanonicalFields.PARENT_SPAN_ID,
  CanonicalFields.TRACE_FLAGS, CanonicalFields.TRACE_STATE, CanonicalFields.LEVEL, 
  CanonicalFields.EVENT, CanonicalFields.EVENT_VERSION, CanonicalFields.SCHEMA_VERSION,
  CanonicalFields.SERVICE_NAME, CanonicalFields.SERVICE_VERSION, CanonicalFields.SERVICE_INSTANCE_ID, 
  CanonicalFields.SERVICE_NAMESPACE, CanonicalFields.DEPLOYMENT_ENVIRONMENT, CanonicalFields.COMPONENT, 
  CanonicalFields.OPERATION, CanonicalFields.DIRECTION, CanonicalFields.DURATION_MS,
  CanonicalFields.STOP_REASON, CanonicalFields.DATA_CLASSIFICATION, CanonicalFields.HTTP_REQUEST_METHOD, 
  CanonicalFields.URL_PATH, CanonicalFields.URL_QUERY, CanonicalFields.URL_FULL, 
  CanonicalFields.HTTP_RESPONSE_STATUS_CODE, CanonicalFields.HTTP_ROUTE, CanonicalFields.USER_AGENT_ORIGINAL,
  CanonicalFields.SERVER_ADDRESS, CanonicalFields.SERVER_PORT, CanonicalFields.CLIENT_ADDRESS, 
  CanonicalFields.NETWORK_PEER_ADDRESS, CanonicalFields.NETWORK_PEER_PORT, CanonicalFields.RPC_SYSTEM, 
  CanonicalFields.RPC_METHOD, CanonicalFields.RPC_JSONRPC_REQUEST_ID, CanonicalFields.RPC_JSONRPC_PARAMS_BYTES, 
  CanonicalFields.RPC_JSONRPC_RESULT_BYTES, CanonicalFields.GEN_AI_PROVIDER_NAME,
  CanonicalFields.GEN_AI_REQUEST_MODEL, CanonicalFields.GEN_AI_USAGE_INPUT_TOKENS, 
  CanonicalFields.GEN_AI_USAGE_OUTPUT_TOKENS, CanonicalFields.SESSION_ID, CanonicalFields.IDE_NAME, 
  CanonicalFields.IDE_VERSION, CanonicalFields.PLUGIN_VERSION, CanonicalFields.OS, CanonicalFields.WORKSPACE_ID,
  CanonicalFields.REPO_HASH, CanonicalFields.USER_ANONYMOUS_ID, CanonicalFields.IDE_SESSION_SEQ, 
  CanonicalFields.REQUEST_IDEMPOTENCY_KEY, CanonicalFields.ERROR_TYPE, CanonicalFields.ERROR_MESSAGE, 
  CanonicalFields.ERROR_STACK, CanonicalFields.ERROR_STACK_TRUNCATED, CanonicalFields.STREAM_ID, 
  CanonicalFields.STREAM_SEQ, CanonicalFields.STREAM_CHUNKS_TOTAL, CanonicalFields.BYTES_TOTAL,
  CanonicalFields.TIME_TO_FIRST_TOKEN, CanonicalFields.TIME_TO_LAST_BYTE, CanonicalFields.CHUNK_INDEX, 
  CanonicalFields.SLOW_KIND, CanonicalFields.HANG_KIND, CanonicalFields.WEBSOCKET_CONNECTION_ID, CanonicalFields.WEBSOCKET_URL, 
  CanonicalFields.WEBSOCKET_PROTOCOLS, CanonicalFields.WEBSOCKET_DIRECTION, CanonicalFields.WEBSOCKET_MESSAGE_TYPE, CanonicalFields.WEBSOCKET_MESSAGE_SIZE, 
  CanonicalFields.WEBSOCKET_CLOSE_CODE, CanonicalFields.WEBSOCKET_CLOSE_REASON, CanonicalFields.MESSAGE_ID, CanonicalFields.MESSAGE_ROLE, CanonicalFields.MESSAGE_CONTENT_LENGTH, 
  CanonicalFields.MESSAGE_HASH, CanonicalFields.CHAT_COMMAND_TYPE, CanonicalFields.CONTEXT_HAS_CONTENT, CanonicalFields.SMART_APPLY_CHANGES_COUNT,
  CanonicalFields.SMART_APPLY_HAS_CHANGES, CanonicalFields.SMART_APPLY_SUCCESS, CanonicalFields.COMMIT_CHANGES_COUNT,
  CanonicalFields.COMMIT_HAS_CHANGES, CanonicalFields.COMMIT_MESSAGE_LENGTH, CanonicalFields.COMMIT_MESSAGE_HASH, CanonicalFields.COMMIT_SUCCESS
]);

/**
 * Проверяет, является ли ключ каноническим
 */
export function isCanonicalKey(key: string): boolean {
  // Проверяем точное соответствие каноническим полям
  if (CANONICAL_FIELDS.has(key as any)) {
    return true;
  }
  
  // Проверяем паттерн для attributes.*
  const attributeKeyPattern = /^attributes\.[a-z][a-z0-9_]*(?:\.[a-z][a-z0-9_]*)*$/;
  return attributeKeyPattern.test(key);
}

/**
 * Валидирует каноничность всех ключей в событии
 */
export function validateCanonicalKeys(event: Record<string, any>): string[] {
  const nonCanonicalKeys: string[] = [];
  
  const checkKeys = (obj: any, path: string = '') => {
    for (const key in obj) {
      const fullPath = path ? `${path}.${key}` : key;
      
      // For top-level keys, check if they are canonical
      // For nested keys, check the full path
      const keyToCheck = path ? fullPath : key;
      
      if (!isCanonicalKey(keyToCheck)) {
        nonCanonicalKeys.push(fullPath);
      }
      
      if (obj[key] && typeof obj[key] === 'object' && !Array.isArray(obj[key])) {
        checkKeys(obj[key], fullPath);
      }
    }
  };
  
  checkKeys(event);
  return nonCanonicalKeys;
}