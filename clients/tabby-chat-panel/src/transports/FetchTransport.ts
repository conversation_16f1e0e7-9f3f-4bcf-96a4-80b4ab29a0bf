/**
 * Fetch Transport для tabby-chat-panel
 * 
 * Создан на основе backup/transports/HTTPTransport.ts с адаптацией под браузерное окружение.
 * Использует Fetch API для отправки трейсов на сервер с батчированием и retry логикой.
 */

import type { TraceEvent } from 'filin-base/browser';
import type { TraceTransport, HTTPConfig } from 'filin-base/browser';
import { TraceConfigBrowser } from '../trace/TraceConfigBrowser';

export interface FetchTransportConfig extends HTTPConfig {
  endpoint: string;
  headers?: Record<string, string>;
  method?: 'POST' | 'PUT';
  batchSize?: number;
  batchTimeout?: number;
  maxRetries?: number;
  retryDelay?: number;
}

export class FetchTransport implements TraceTransport {
  public readonly name = 'fetch';
  public readonly enabled: boolean;

  private config: FetchTransportConfig;
  private batchBuffer: TraceEvent[] = [];
  private batchTimer: number | null = null;

  constructor(config: FetchTransportConfig) {
    this.config = {
      method: 'POST',
      batchSize: 50,
      batchTimeout: 10000,
      maxRetries: 3,
      retryDelay: 1000,
      ...config
    };
    this.enabled = config.enabled && !!config.endpoint;
  }

  async send(event: TraceEvent): Promise<void> {
    if (!this.enabled) return;

    // Добавляем в батч
    this.batchBuffer.push(event);

    // Если батч заполнен, отправляем немедленно
    if (this.batchBuffer.length >= (this.config.batchSize || 50)) {
      await this.flushBatch();
    } else {
      // Устанавливаем таймер для отправки батча
      this.scheduleBatchFlush();
    }
  }

  async sendBatch(events: TraceEvent[]): Promise<void> {
    if (!this.enabled || events.length === 0) return;

    const maxRetries = this.config.maxRetries || 3;
    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        await this.sendFetchRequest(events);
        return; // Успешно отправлено
      } catch (error) {
        lastError = error as Error;

        if (attempt < maxRetries) {
          // Ждем перед повторной попыткой (экспоненциальный backoff)
          const delay = (this.config.retryDelay || 1000) * Math.pow(2, attempt);
          await this.sleep(delay);
        }
      }
    }

    // Если все попытки неудачны, выбрасываем последнюю ошибку
    throw lastError;
  }

  private async sendFetchRequest(events: TraceEvent[]): Promise<void> {
    const method = this.config.method || 'POST';
    const serviceInfo = TraceConfigBrowser.getServiceInfo();
    const headers = {
      'Content-Type': 'application/x-ndjson',
      'User-Agent': `${serviceInfo.name}/${serviceInfo.version}`,
      ...this.config.headers,
    };

    // Отправляем в JSON Lines формате
    const body = events.map(event => JSON.stringify(event)).join('\n');

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 секунд таймаут

    try {
      const response = await fetch(this.config.endpoint, {
        method,
        headers,
        body,
        signal: controller.signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } finally {
      clearTimeout(timeoutId);
    }
  }

  private scheduleBatchFlush(): void {
    if (this.batchTimer) return;

    const timeout = this.config.batchTimeout || 10000;
    this.batchTimer = window.setTimeout(async () => {
      await this.flushBatch();
    }, timeout);
  }

  private async flushBatch(): Promise<void> {
    if (this.batchTimer) {
      window.clearTimeout(this.batchTimer);
      this.batchTimer = null;
    }

    if (this.batchBuffer.length === 0) return;

    const events = [...this.batchBuffer];
    this.batchBuffer = [];

    try {
      await this.sendBatch(events);
    } catch (error) {
      console.error('[FetchTransport] Failed to send batch:', error);
      // В случае ошибки можно реализовать fallback на LocalStorage
      throw error;
    }
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async close(): Promise<void> {
    if (this.batchTimer) {
      window.clearTimeout(this.batchTimer);
      this.batchTimer = null;
    }

    // Отправляем оставшиеся события
    if (this.batchBuffer.length > 0) {
      await this.flushBatch();
    }
  }

  /**
   * Принудительная отправка накопленных событий
   */
  async flush(): Promise<void> {
    await this.flushBatch();
  }

  /**
   * Получает статистику батча
   */
  getBatchStats(): { pendingEvents: number; batchSize: number; batchTimeout: number } {
    return {
      pendingEvents: this.batchBuffer.length,
      batchSize: this.config.batchSize || 50,
      batchTimeout: this.config.batchTimeout || 10000
    };
  }

  /**
   * Проверяет доступность Fetch API
   */
  static isAvailable(): boolean {
    return typeof window !== 'undefined' && typeof fetch !== 'undefined';
  }
}