/**
 * Filin Base - Базовые интерфейсы транспортов
 *
 * Содержит только базовые типы и интерфейсы для транспортов.
 * Конкретные реализации создаются в каждом клиенте.
 *
 * ВНИМАНИЕ: otlp-collector исключен из основного экспорта, так как содержит
 * Node.js зависимости (http модуль) и не совместим с браузерными бандлами.
 * Для серверного использования используйте filin-base/server или импортируйте напрямую:
 * import { OTLPCollector } from 'filin-base/transports/otlp-collector';
 */

export * from './types';
export * from './manager';
export * from './otlp';
export * from './otlp-base';