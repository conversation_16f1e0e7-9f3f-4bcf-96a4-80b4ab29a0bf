/**
 * Filin Tracer - Браузерные утилиты трассировки для tabby-chat-panel
 * 
 * Браузерная версия TraceUtilsBase без использования process.env
 */

// Простая реализация UUID для браузера без внешних зависимостей
function generateUUID(): string {
  // Используем crypto.randomUUID если доступно (современные браузеры)
  if (typeof crypto !== 'undefined' && crypto.randomUUID) {
    return crypto.randomUUID();
  }

  // Fallback для старых браузеров
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}
import {
  CanonicalFields,
  TraceEvent,
  TraceEventType,
  LogLevel,
  TraceConfig,
  DataMasker,
  StopReason,
  TraceEventTypes,
  FILIN_SCHEMA_ID
} from 'filin-base';
import { W3CTraceContext, W3CBaggage } from 'filin-base/browser';
import type { SpanContext } from 'filin-base/browser';
import { TraceConfigBrowser } from './TraceConfigBrowser';

/**
 * Браузерные утилиты для работы с trace_id и событиями
 */
export class TraceUtilsBrowser {

  /**
   * Генерирует уникальный trace_id в формате W3C Trace Context (32 hex символа)
   * @return trace_id в формате W3C
   */
  static generateTraceId(): string {
    const uuid = generateUUID().replace(/-/g, '');
    return uuid.substring(0, 32);
  }

  /**
   * Генерирует уникальный span_id в формате W3C Trace Context (16 hex символов)
   * @return span_id в формате W3C
   */
  static generateSpanId(): string {
    const uuid = generateUUID().replace(/-/g, '');
    return uuid.substring(0, 16);
  }

  /**
   * Создает базовое событие трассировки в формате JSON Lines (браузерная версия)
   * @param event тип события
   * @param traceId идентификатор трассировки
   * @param spanId идентификатор спана
   * @param parentSpanId идентификатор родительского спана (опционально)
   * @param serviceInfo информация о сервисе
   * @param component компонент
   * @param level уровень логирования
   * @return базовое событие TraceEvent
   */
  static createBaseEventFull(
    event: TraceEventType,
    traceId: string,
    spanId: string,
    parentSpanId: string | undefined,
    serviceInfo: {
      name: string;
      version: string;
      instanceId: string;
      namespace: string;
      environment: string;
    },
    component: string,
    level: LogLevel = "INFO"
  ): TraceEvent {
    const now = new Date();
    const tsUnixNanos = (BigInt(now.getTime()) * BigInt(1000000)).toString();

    const baseEvent: TraceEvent = {
      [CanonicalFields.TIMESTAMP]: now.toISOString(),
      [CanonicalFields.TIMESTAMP_UNIX_NANOS]: tsUnixNanos,
      [CanonicalFields.EVENT_ID]: TraceUtilsBrowser.generateSpanId(), // Используем spanId как eventId
      [CanonicalFields.TRACE_ID]: traceId,
      [CanonicalFields.SPAN_ID]: spanId,
      [CanonicalFields.LEVEL]: level,
      [CanonicalFields.EVENT]: event,
      [CanonicalFields.EVENT_VERSION]: "1.0",
      [CanonicalFields.SCHEMA_VERSION]: FILIN_SCHEMA_ID,
      [CanonicalFields.SERVICE_NAME]: serviceInfo.name,
      [CanonicalFields.SERVICE_VERSION]: serviceInfo.version,
      [CanonicalFields.SERVICE_INSTANCE_ID]: serviceInfo.instanceId,
      [CanonicalFields.SERVICE_NAMESPACE]: serviceInfo.namespace,
      [CanonicalFields.DEPLOYMENT_ENVIRONMENT]: serviceInfo.environment,
      [CanonicalFields.COMPONENT]: component
    };

    if (parentSpanId) {
      baseEvent[CanonicalFields.PARENT_SPAN_ID] = parentSpanId;
    }

    return baseEvent;
  }

  // Алиас для совместимости
  static createBaseEvent = TraceUtilsBrowser.createBaseEventFull;

  /**
   * Создает событие трассировки с базовыми полями
   * @param eventType тип события
   * @param traceId идентификатор трассировки
   * @param spanId идентификатор span
   * @param data дополнительные данные
   * @return событие трассировки
   */
  static createTraceEvent(
    eventType: TraceEventType,
    traceId: string,
    spanId: string,
    data: Record<string, any> = {}
  ): TraceEvent {
    const now = new Date();
    const serviceInfo = TraceConfigBrowser.getServiceInfo();
    return {
      [CanonicalFields.TIMESTAMP]: now.toISOString(),
      [CanonicalFields.TIMESTAMP_UNIX_NANOS]: (now.getTime() * 1000000).toString(),
      [CanonicalFields.EVENT_ID]: TraceUtilsBrowser.generateSpanId(),
      [CanonicalFields.TRACE_ID]: traceId,
      [CanonicalFields.SPAN_ID]: spanId,
      [CanonicalFields.EVENT]: eventType,
      [CanonicalFields.EVENT_VERSION]: "1.0",
      [CanonicalFields.SCHEMA_VERSION]: "filin.log.v1",
      [CanonicalFields.SERVICE_NAME]: serviceInfo.name,
      [CanonicalFields.SERVICE_VERSION]: serviceInfo.version,
      [CanonicalFields.SERVICE_INSTANCE_ID]: serviceInfo.instanceId,
      [CanonicalFields.SERVICE_NAMESPACE]: serviceInfo.namespace,
      [CanonicalFields.DEPLOYMENT_ENVIRONMENT]: serviceInfo.environment,
      [CanonicalFields.COMPONENT]: serviceInfo.name,
      [CanonicalFields.LEVEL]: 'INFO',
      ...data
    };
  }

  /**
   * Маскирует чувствительные данные в объекте
   * @param data данные для маскировки
   * @param config конфигурация трассировки
   * @return замаскированные данные
   */
  static maskSensitiveData(data: any, config?: TraceConfig): any {
    const traceConfig = config || TraceConfigBrowser.loadDefaultConfig();
    if (!traceConfig.maskTokens) {
      return data;
    }

    const masker = new DataMasker(traceConfig);
    if (typeof data === 'string') {
      return masker.maskSensitiveData(data).maskedData;
    }
    // Для объектов используем простую сериализацию и маскировку
    const jsonString = JSON.stringify(data);
    return masker.maskSensitiveData(jsonString).maskedData;
  }

  /**
   * Создает W3C Trace Context заголовок
   * @param traceId идентификатор трассировки
   * @param spanId идентификатор span
   * @param sampled флаг семплирования
   * @return строка traceparent
   */
  static createW3CTraceparent(traceId: string, spanId: string, sampled: boolean = true): string {
    return W3CTraceContext.createTraceparent(traceId, spanId, sampled);
  }

  /**
   * Парсит W3C Trace Context из строки
   * @param traceParent строка traceparent
   * @return объект с traceId и spanId или null
   */
  static parseW3CTraceContext(traceParent: string): { traceId: string; spanId: string } | null {
    try {
      const parsed = W3CTraceContext.parseTraceparent(traceParent);
      return parsed ? { traceId: parsed.traceId, spanId: parsed.spanId } : null;
    } catch (error) {
      console.warn('[FilinTracer] Failed to parse W3C Trace Context:', error);
      return null;
    }
  }

  /**
   * Создает контекст распространения трассировки
   * @param traceId идентификатор трассировки
   * @param spanId идентификатор span
   * @param baggage багаж (опционально)
   * @return контекст распространения
   */
  static createPropagationContext(traceId: string, spanId: string, baggage?: string): SpanContext {
    const traceparent = TraceUtilsBrowser.createW3CTraceparent(traceId, spanId);
    return {
      traceId,
      spanId,
      traceparent,
      baggage
    };
  }

  /**
   * Форматирует длительность операции
   * @param startTime время начала (timestamp)
   * @param endTime время окончания (timestamp, по умолчанию текущее время)
   * @return длительность в миллисекундах
   */
  static calculateDuration(startTime: number, endTime?: number): number {
    const end = endTime || Date.now();
    return Math.max(0, end - startTime);
  }

  /**
   * Определяет причину остановки операции
   * @param duration длительность операции в мс
   * @param error ошибка (если есть)
   * @param config конфигурация трассировки
   * @return причина остановки
   */
  static determineStopReason(duration: number, error?: Error, config?: TraceConfig): StopReason {
    if (error) {
      return 'error';
    }

    const traceConfig = config || TraceConfigBrowser.loadDefaultConfig();
    if (duration >= traceConfig.operationHangMs) {
      return 'deadline_exceeded';
    }

    return 'complete';
  }

  /**
   * Создает событие начала операции
   * @param operation название операции
   * @param traceId идентификатор трассировки
   * @param spanId идентификатор span
   * @param data дополнительные данные
   * @return событие трассировки
   */
  static createStartEvent(
    operation: string,
    traceId: string,
    spanId: string,
    data: Record<string, any> = {}
  ): TraceEvent {
    return TraceUtilsBrowser.createTraceEvent(TraceEventTypes.SPAN_START, traceId, spanId, {
      operation,
      ...data
    });
  }

  /**
   * Создает событие окончания операции
   * @param operation название операции
   * @param traceId идентификатор трассировки
   * @param spanId идентификатор span
   * @param duration длительность в мс
   * @param stopReason причина остановки
   * @param data дополнительные данные
   * @return событие трассировки
   */
  static createEndEvent(
    operation: string,
    traceId: string,
    spanId: string,
    duration: number,
    stopReason: StopReason = 'complete',
    data: Record<string, any> = {}
  ): TraceEvent {
    return TraceUtilsBrowser.createTraceEvent(TraceEventTypes.SPAN_END, traceId, spanId, {
      operation,
      duration_ms: duration,
      stop_reason: stopReason,
      ...data
    });
  }

  /**
   * Создает событие ошибки
   * @param operation название операции
   * @param traceId идентификатор трассировки
   * @param spanId идентификатор span
   * @param error ошибка
   * @param data дополнительные данные
   * @return событие трассировки
   */
  static createErrorEvent(
    operation: string,
    traceId: string,
    spanId: string,
    error: Error,
    data: Record<string, any> = {}
  ): TraceEvent {
    return TraceUtilsBrowser.createTraceEvent(TraceEventTypes.HTTP_ERROR, traceId, spanId, {
      operation,
      "error.message": error.message,
      "error.type": error.name,
      "error.stack": error.stack,
      level: 'ERROR',
      ...data
    });
  }

  /**
   * Проверяет является ли операция медленной
   * @param operation тип операции
   * @param duration длительность в мс
   * @param config конфигурация трассировки
   * @return true если операция медленная
   */
  static isSlowOperation(operation: string, duration: number, config?: TraceConfig): boolean {
    const traceConfig = config || TraceConfigBrowser.loadDefaultConfig();

    // Специальные пороги для разных типов операций
    if (operation.includes('autocomplete') || operation.includes('completion')) {
      return duration >= traceConfig.autocompleteSlowMs;
    }

    if (operation.includes('chat') || operation.includes('message')) {
      return duration >= traceConfig.chatSlowMs;
    }

    if (operation.includes('http') || operation.includes('fetch')) {
      return duration >= traceConfig.httpTimeoutMs;
    }

    if (operation.includes('lsp') || operation.includes('language_server')) {
      return duration >= traceConfig.lspTimeoutMs;
    }

    // Общий порог для остальных операций
    return duration >= traceConfig.slowOperationMs;
  }

  /**
   * Создает уникальный идентификатор для браузерной сессии
   * @return идентификатор сессии
   */
  static generateSessionId(): string {
    return `browser-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
  }

  /**
   * Создает Filin baggage заголовок с graceful degradation
   * @param ide название IDE
   * @param workspace идентификатор workspace
   * @param session идентификатор сессии
   * @return baggage строка или undefined при ошибке
   */
  static createFilinBaggage(ide: string, workspace: string, session: string): string | undefined {
    try {
      return W3CBaggage.createFilinBaggage(ide, workspace, session);
    } catch (error) {
      console.warn('[FilinTracer] Failed to create baggage:', error);
      return undefined;
    }
  }

  /**
   * Парсит baggage заголовок с graceful degradation
   * @param baggage baggage строка
   * @return объект с данными или пустой объект при ошибке
   */
  static parseBaggage(baggage: string): { ide?: string; workspace?: string; session?: string } {
    try {
      return W3CBaggage.getFilinData(baggage);
    } catch (error) {
      console.warn('[FilinTracer] Failed to parse baggage:', error);
      return {};
    }
  }

  /**
   * Валидирует baggage содержимое с graceful degradation
   * @param baggage baggage строка
   * @return true если валидно, false при ошибке
   */
  static validateBaggage(baggage: string): boolean {
    try {
      return W3CBaggage.validateBaggageContent(baggage);
    } catch (error) {
      console.warn('[FilinTracer] Baggage validation failed:', error);
      return false;
    }
  }

  /**
   * Получает информацию о браузерной среде
   * @return информация о среде
   */
  static getBrowserEnvironment(): Record<string, any> {
    return {
      user_agent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown',
      platform: typeof navigator !== 'undefined' ? ((navigator as any).userAgentData?.platform || 'unknown') : 'unknown',
      language: typeof navigator !== 'undefined' ? navigator.language : 'unknown',
      viewport_width: typeof window !== 'undefined' ? window.innerWidth : 0,
      viewport_height: typeof window !== 'undefined' ? window.innerHeight : 0,
      timestamp: new Date().toISOString()
    };
  }
}

// Для совместимости экспортируем как TraceUtilsBase
export { TraceUtilsBrowser as TraceUtilsBase };