/**
 * Тесты для канонических типов трассировки tabby-chat-panel
 */

import { describe, it, expect } from 'vitest';
import { CanonicalFields } from 'filin-base/browser';
import {
  migrateFieldNames,
  isCanonicalKey,
  validateCanonicalKeys,
  FIELD_MIGRATION_TABLE,
  type CanonicalTraceEvent
} from '../CanonicalTypes';

describe('CanonicalTypes', () => {
  describe('migrateFieldNames', () => {
    it('should migrate old field names to canonical names', () => {
      const oldEvent = {
        traceId: 'abc123',
        spanId: 'def456',
        'model.name': 'gpt-4',
        'prompt.tokens': 100,
        'completion.tokens': 50,
        timestamp: '2025-01-15T10:00:00Z'
      };

      const migratedEvent = migrateFieldNames(oldEvent);

      expect(migratedEvent).toEqual({
        [CanonicalFields.TRACE_ID]: 'abc123',
        [CanonicalFields.SPAN_ID]: 'def456',
        [CanonicalFields.GEN_AI_REQUEST_MODEL]: 'gpt-4',
        [CanonicalFields.GEN_AI_USAGE_INPUT_TOKENS]: 100,
        [CanonicalFields.GEN_AI_USAGE_OUTPUT_TOKENS]: 50,
        [CanonicalFields.TIMESTAMP]: '2025-01-15T10:00:00Z'
      });
    });

    it('should preserve canonical field names', () => {
      const canonicalEvent = {
        [CanonicalFields.TRACE_ID]: 'abc123',
        [CanonicalFields.SPAN_ID]: 'def456',
        [CanonicalFields.GEN_AI_REQUEST_MODEL]: 'gpt-4',
        [CanonicalFields.GEN_AI_USAGE_INPUT_TOKENS]: 100
      };

      const migratedEvent = migrateFieldNames(canonicalEvent);

      expect(migratedEvent).toEqual(canonicalEvent);
    });

    it('should handle mixed old and canonical field names', () => {
      const mixedEvent = {
        traceId: 'abc123',
        'span.id': 'def456',
        'model.name': 'gpt-4',
        'gen_ai.usage.input_tokens': 100
      };

      const migratedEvent = migrateFieldNames(mixedEvent);

      expect(migratedEvent).toEqual({
        [CanonicalFields.TRACE_ID]: 'abc123',
        [CanonicalFields.SPAN_ID]: 'def456',
        [CanonicalFields.GEN_AI_REQUEST_MODEL]: 'gpt-4',
        [CanonicalFields.GEN_AI_USAGE_INPUT_TOKENS]: 100
      });
    });
  });

  describe('isCanonicalKey', () => {
    it('should validate canonical field names', () => {
      expect(isCanonicalKey(CanonicalFields.TRACE_ID)).toBe(true);
      expect(isCanonicalKey(CanonicalFields.SPAN_ID)).toBe(true);
      expect(isCanonicalKey(CanonicalFields.GEN_AI_REQUEST_MODEL)).toBe(true);
      expect(isCanonicalKey(CanonicalFields.GEN_AI_USAGE_INPUT_TOKENS)).toBe(true);
      expect(isCanonicalKey(CanonicalFields.HTTP_REQUEST_METHOD)).toBe(true);
      expect(isCanonicalKey(CanonicalFields.URL_PATH)).toBe(true);
      expect(isCanonicalKey(CanonicalFields.RPC_METHOD)).toBe(true);
      expect(isCanonicalKey(CanonicalFields.WEBSOCKET_CONNECTION_ID)).toBe(true);
    });

    it('should validate attributes pattern', () => {
      expect(isCanonicalKey('attributes.custom_field')).toBe(true);
      expect(isCanonicalKey('attributes.nested.field')).toBe(true);
      expect(isCanonicalKey('attributes.deep.nested.field')).toBe(true);
    });

    it('should reject non-canonical field names', () => {
      expect(isCanonicalKey('traceId')).toBe(false);
      expect(isCanonicalKey('spanId')).toBe(false);
      expect(isCanonicalKey('model.name')).toBe(false);
      expect(isCanonicalKey('prompt.tokens')).toBe(false);
      expect(isCanonicalKey('http.method')).toBe(false);
      expect(isCanonicalKey('CamelCase')).toBe(false);
      expect(isCanonicalKey('UPPER_CASE')).toBe(false);
    });

    it('should reject invalid attribute patterns', () => {
      expect(isCanonicalKey('attributes.CamelCase')).toBe(false);
      expect(isCanonicalKey('attributes.UPPER_CASE')).toBe(false);
      expect(isCanonicalKey('attributes.123invalid')).toBe(false);
      expect(isCanonicalKey('attributes.')).toBe(false);
    });
  });

  describe('validateCanonicalKeys', () => {
    it('should return empty array for valid canonical event', () => {
      const validEvent = {
        [CanonicalFields.TRACE_ID]: 'abc123',
        [CanonicalFields.SPAN_ID]: 'def456',
        [CanonicalFields.GEN_AI_REQUEST_MODEL]: 'gpt-4',
        [CanonicalFields.GEN_AI_USAGE_INPUT_TOKENS]: 100,
        'attributes.custom_field': 'value'
      };

      const nonCanonicalKeys = validateCanonicalKeys(validEvent);
      expect(nonCanonicalKeys).toEqual([]);
    });

    it('should detect non-canonical keys', () => {
      const invalidEvent = {
        traceId: 'abc123',
        'span.id': 'def456',
        'model.name': 'gpt-4',
        promptTokens: 100
      };

      const nonCanonicalKeys = validateCanonicalKeys(invalidEvent);
      expect(nonCanonicalKeys).toContain('traceId');
      expect(nonCanonicalKeys).toContain('model.name');
      expect(nonCanonicalKeys).toContain('promptTokens');
      expect(nonCanonicalKeys).not.toContain(CanonicalFields.SPAN_ID);
    });

    it('should detect nested non-canonical keys', () => {
      const nestedEvent = {
        [CanonicalFields.TRACE_ID]: 'abc123',
        nested: {
          invalidKey: 'value',
          'valid_key': 'value'
        }
      };

      const nonCanonicalKeys = validateCanonicalKeys(nestedEvent);
      expect(nonCanonicalKeys).toContain('nested');
      expect(nonCanonicalKeys).toContain('nested.invalidKey');
      expect(nonCanonicalKeys).not.toContain(CanonicalFields.TRACE_ID);
    });
  });

  describe('FIELD_MIGRATION_TABLE', () => {
    it('should contain GenAI field mappings', () => {
      expect(FIELD_MIGRATION_TABLE['model.name']).toBe(CanonicalFields.GEN_AI_REQUEST_MODEL);
      expect(FIELD_MIGRATION_TABLE['modelName']).toBe(CanonicalFields.GEN_AI_REQUEST_MODEL);
      expect(FIELD_MIGRATION_TABLE['prompt.tokens']).toBe(CanonicalFields.GEN_AI_USAGE_INPUT_TOKENS);
      expect(FIELD_MIGRATION_TABLE['promptTokens']).toBe(CanonicalFields.GEN_AI_USAGE_INPUT_TOKENS);
      expect(FIELD_MIGRATION_TABLE['completion.tokens']).toBe(CanonicalFields.GEN_AI_USAGE_OUTPUT_TOKENS);
      expect(FIELD_MIGRATION_TABLE['completionTokens']).toBe(CanonicalFields.GEN_AI_USAGE_OUTPUT_TOKENS);
    });

    it('should contain HTTP field mappings', () => {
      expect(FIELD_MIGRATION_TABLE['http.method']).toBe(CanonicalFields.HTTP_REQUEST_METHOD);
      expect(FIELD_MIGRATION_TABLE['http.target']).toBe(CanonicalFields.URL_PATH);
      expect(FIELD_MIGRATION_TABLE['http.status.code']).toBe(CanonicalFields.HTTP_RESPONSE_STATUS_CODE);
    });

    it('should contain identifier field mappings', () => {
      expect(FIELD_MIGRATION_TABLE['traceId']).toBe(CanonicalFields.TRACE_ID);
      expect(FIELD_MIGRATION_TABLE['spanId']).toBe(CanonicalFields.SPAN_ID);
      expect(FIELD_MIGRATION_TABLE['parentSpanId']).toBe(CanonicalFields.PARENT_SPAN_ID);
    });

    it('should contain streaming field mappings', () => {
      expect(FIELD_MIGRATION_TABLE['stream_id']).toBe(CanonicalFields.STREAM_ID);
      expect(FIELD_MIGRATION_TABLE['time_to_first_token']).toBe(CanonicalFields.TIME_TO_FIRST_TOKEN);
      expect(FIELD_MIGRATION_TABLE['time_to_last_byte']).toBe(CanonicalFields.TIME_TO_LAST_BYTE);
    });

    it('should contain WebSocket field mappings', () => {
      expect(FIELD_MIGRATION_TABLE['websocket_connection_id']).toBe(CanonicalFields.WEBSOCKET_CONNECTION_ID);
      expect(FIELD_MIGRATION_TABLE['websocket_url']).toBe(CanonicalFields.WEBSOCKET_URL);
      expect(FIELD_MIGRATION_TABLE['websocket_protocols']).toBe(CanonicalFields.WEBSOCKET_PROTOCOLS);
    });
  });

  describe('CanonicalTraceEvent type', () => {
    it('should accept valid canonical event structure', () => {
      const event: CanonicalTraceEvent = {
        [CanonicalFields.TIMESTAMP]: '2025-01-15T10:00:00.000Z',
        [CanonicalFields.TIMESTAMP_UNIX_NANOS]: '1736936400000000000',
        [CanonicalFields.EVENT_ID]: '01936d4a-1234-7890-abcd-123456789012',
        [CanonicalFields.TRACE_ID]: 'abcdef1234567890abcdef1234567890',
        [CanonicalFields.SPAN_ID]: 'abcdef1234567890',
        [CanonicalFields.LEVEL]: 'INFO',
        [CanonicalFields.EVENT]: 'CHAT_MESSAGE_SENT',
        [CanonicalFields.EVENT_VERSION]: '1.0',
        [CanonicalFields.SCHEMA_VERSION]: 'filin.log.v1',
        [CanonicalFields.SERVICE_NAME]: 'tabby-chat-panel',
        [CanonicalFields.SERVICE_VERSION]: '0.10.0',
        [CanonicalFields.SERVICE_INSTANCE_ID]: 'chat-panel-001',
        [CanonicalFields.SERVICE_NAMESPACE]: 'filin.chat',
        [CanonicalFields.DEPLOYMENT_ENVIRONMENT]: 'development',
        [CanonicalFields.COMPONENT]: 'chat-handler',
        [CanonicalFields.GEN_AI_REQUEST_MODEL]: 'gpt-4',
        [CanonicalFields.GEN_AI_USAGE_INPUT_TOKENS]: 100,
        [CanonicalFields.GEN_AI_USAGE_OUTPUT_TOKENS]: 50,
        'attributes.custom_field': 'value'
      };

      expect(event[CanonicalFields.GEN_AI_REQUEST_MODEL]).toBe('gpt-4');
      expect(event[CanonicalFields.GEN_AI_USAGE_INPUT_TOKENS]).toBe(100);
      expect(event['attributes.custom_field']).toBe('value');
    });
  });
});