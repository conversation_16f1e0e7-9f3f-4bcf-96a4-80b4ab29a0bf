/**
 * Filin Tracer - React Hooks для Chat Panel
 * 
 * Принцип: React-специфичные утилиты трассировки с W3C Trace Context
 * Интеграция: Использует ChatTraceUtils для основной функциональности
 * Использование: Hooks для интеграции трассировки с React компонентами
 */

import React, { useCallback, useRef, useState, useContext, createContext } from 'react';
import { ConfigLoaderBase } from '../config/ConfigLoader';
import { TraceConfigBrowser } from './TraceConfigBrowser';
import {
  chatTracer,
  ChatOperationType,
  generateTraceId,
  generateSpanId,
  type ChatMessage,
  type SSEStreamContext,
  type WebSocketTraceContext
} from './TraceUtils';
import { CanonicalFields } from "filin-base/browser";
import { BrowserAdapter, type TraceAdapter } from '../adapters/BrowserAdapter';
import type { ClientApi } from '../client';
import type { ServerApi, ChatCommand } from '../server';

// React Context для BrowserAdapter
const BrowserAdapterContext = createContext<TraceAdapter | null>(null);

export function BrowserAdapterProvider({ children }: { children: React.ReactNode }) {
  const [adapter] = useState(() => new BrowserAdapter({
    telemetryEndpoint: '/api/telemetry',
    enableLocalStorage: true,
    maxStoredEvents: 1000
  }));

  return React.createElement(BrowserAdapterContext.Provider, { value: adapter }, children);
}

export function useBrowserAdapter(): TraceAdapter {
  const adapter = useContext(BrowserAdapterContext);
  if (!adapter) {
    throw new Error('useBrowserAdapter must be used within BrowserAdapterProvider');
  }
  return adapter;
}

/**
 * React hook для трассировки чат-сообщений с W3C Trace Context
 */
export function useChatTracing() {
  const adapter = useBrowserAdapter();
  const activeSpans = useRef<Map<string, { traceId: string; spanId: string; parentSpanId?: string }>>(new Map());

  const traceMessage = useCallback((message: ChatMessage, traceId?: string, parentSpanId?: string) => {
    if (!TraceConfigBrowser.isTracingEnabled()) return { traceId: traceId || generateTraceId(), spanId: generateSpanId() };

    return adapter.trace('chat_message_sent', () => {
      const result = chatTracer.chatMessageSent(message, traceId, parentSpanId);
      activeSpans.current.set(message.id, result);
      return result;
    });
  }, [adapter]);

  const traceResponse = useCallback((message: ChatMessage, traceId: string, spanId: string, duration: number, parentSpanId?: string) => {
    if (!TraceConfigBrowser.isTracingEnabled()) return;

    adapter.trace('chat_response_received', () => {
      chatTracer.chatResponseReceived(message, traceId, spanId, duration, parentSpanId);
    });
  }, [adapter]);

  const traceCommand = useCallback((command: ChatCommand, code: string, traceId?: string, parentSpanId?: string) => {
    if (!TraceConfigBrowser.isTracingEnabled()) return { traceId: traceId || generateTraceId(), spanId: generateSpanId() };

    return adapter.trace('chat_command', () => {
      const chatCommand = {
        type: ChatOperationType.CHAT_REQUEST,
        content: code,
        context: { command },
        traceId,
        spanId: generateSpanId(),
        parentSpanId
      };

      return chatTracer.chatCommand(chatCommand, traceId, parentSpanId);
    });
  }, [adapter]);

  const traceSmartApply = useCallback((changes: any, success: boolean, duration: number, traceId?: string, parentSpanId?: string) => {
    if (!TraceConfigBrowser.isTracingEnabled()) return { traceId: traceId || generateTraceId(), spanId: generateSpanId() };

    return adapter.trace('smart_apply', () => {
      return chatTracer.smartApply(changes, success, duration, traceId, parentSpanId);
    });
  }, [adapter]);

  const traceGenerateCommit = useCallback((changes: any, generatedMessage: string, success: boolean, duration: number, traceId?: string, parentSpanId?: string) => {
    if (!TraceConfigBrowser.isTracingEnabled()) return { traceId: traceId || generateTraceId(), spanId: generateSpanId() };

    return adapter.trace('generate_commit_message', () => {
      return chatTracer.generateCommitMessage(changes, generatedMessage, success, duration, traceId, parentSpanId);
    });
  }, [adapter]);

  const startSpan = useCallback((operation: string, traceId?: string, parentSpanId?: string) => {
    if (!TraceConfigBrowser.isTracingEnabled()) return { traceId: traceId || generateTraceId(), spanId: generateSpanId() };

    const span = adapter.createSpan(operation);
    return { traceId: span.traceId, spanId: span.spanId };
  }, [adapter]);

  const endSpan = useCallback((traceId: string, spanId: string, parentSpanId: string | undefined, duration: number, stopReason?: 'complete' | 'canceled_by_user' | 'deadline_exceeded' | 'error') => {
    if (!TraceConfigBrowser.isTracingEnabled()) return;

    // BrowserAdapter handles span ending automatically, but we can still log the end event
    chatTracer.endSpan(traceId, spanId, parentSpanId, duration, stopReason);
  }, [adapter]);

  const logSlowOperation = useCallback((operation: string, duration: number, slowKind: 'autocomplete' | 'chat' | 'http' | 'lsp' | 'server', traceId: string, spanId: string, parentSpanId?: string) => {
    chatTracer.logSlowOperation(operation, duration, slowKind, traceId, spanId, parentSpanId);
  }, []);

  const logTimeout = useCallback((operation: string, timeoutType: 'HTTP_TIMEOUT' | 'LSP_TIMEOUT', duration: number, traceId: string, spanId: string, parentSpanId?: string) => {
    chatTracer.logTimeout(operation, timeoutType, duration, traceId, spanId, parentSpanId);
  }, []);

  const logHangDetected = useCallback((operation: string, duration: number, hangKind: 'ui_stall' | 'io_wait' | 'lock_contention' | 'external_dependency' | 'unknown', traceId: string, spanId: string, parentSpanId?: string) => {
    chatTracer.logHangDetected(operation, duration, hangKind, traceId, spanId, parentSpanId);
  }, []);

  return {
    traceMessage,
    traceResponse,
    traceCommand,
    traceSmartApply,
    traceGenerateCommit,
    startSpan,
    endSpan,
    logSlowOperation,
    logTimeout,
    logHangDetected,
    generateTraceId,
    activeSpans: activeSpans.current
  };
}

/**
 * React hook для трассировки SSE стриминга с time_to_first_token измерениями
 */
export function useSSETracing() {
  const adapter = useBrowserAdapter();
  const activeStreams = useRef<Map<string, SSEStreamContext>>(new Map());

  const createTracedSSEFetch = useCallback(async (
    url: string,
    options: RequestInit = {},
    traceId?: string,
    parentSpanId?: string,
    idempotencyKey?: string
  ) => {
    if (!TraceConfigBrowser.isTracingEnabled()) {
      const response = await fetch(url, options);
      const mockContext: SSEStreamContext = {
        streamId: `mock-${Date.now()}`,
        traceId: traceId || generateTraceId(),
        spanId: generateSpanId(),
        parentSpanId,
        startTime: Date.now(),
        chunkCount: 0,
        totalBytes: 0
      };
      return { response, context: mockContext };
    }

    return adapter.traceAsync('sse_fetch', async () => {
      const result = await chatTracer.createTracedSSEFetch(url, options, traceId, parentSpanId, idempotencyKey);
      activeStreams.current.set(result.context.streamId, result.context);
      return result;
    });
  }, [adapter]);

  const logChunk = useCallback((streamId: string, chunkData: { content?: string; tokens?: number }, isFirstToken: boolean = false) => {
    const context = activeStreams.current.get(streamId);
    if (context) {
      chatTracer.logSSEChunk(context, chunkData, isFirstToken);
    }
  }, []);

  const endStream = useCallback((streamId: string, stopReason?: 'complete' | 'canceled_by_user' | 'deadline_exceeded' | 'error') => {
    const context = activeStreams.current.get(streamId);
    if (context) {
      chatTracer.endSSEStream(context, stopReason);
      activeStreams.current.delete(streamId);
    }
  }, []);

  const getStreamMetrics = useCallback((streamId: string) => {
    const context = activeStreams.current.get(streamId);
    if (!context) return null;

    const now = Date.now();
    return {
      streamId: context.streamId,
      traceId: context.traceId,
      spanId: context.spanId,
      chunkCount: context.chunkCount,
      totalBytes: context.totalBytes,
      totalTokens: context.totalTokens || 0,
      timeToFirstToken: context.firstTokenTime ? context.firstTokenTime - context.startTime : undefined,
      timeToLastByte: context.lastByteTime ? context.lastByteTime - context.startTime : undefined,
      elapsedTime: now - context.startTime
    };
  }, []);

  const createSSEReader = useCallback((response: Response, streamId: string) => {
    const context = activeStreams.current.get(streamId);
    if (!context || !response.body) return null;

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';

    return {
      async read() {
        const { done, value } = await reader.read();

        if (done) {
          endStream(streamId, 'complete');
          return { done: true, value: undefined };
        }

        const chunk = decoder.decode(value, { stream: true });
        buffer += chunk;

        // Обработка SSE событий
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') {
              endStream(streamId, 'complete');
              return { done: true, value: undefined };
            }

            try {
              const parsed = JSON.parse(data);
              const isFirstToken = context.chunkCount === 0;

              logChunk(streamId, {
                content: parsed.choices?.[0]?.delta?.content || '',
                tokens: 1
              }, isFirstToken);

              return { done: false, value: parsed };
            } catch (error) {
              console.warn('Failed to parse SSE data:', error);
            }
          }
        }

        return { done: false, value: chunk };
      },

      cancel() {
        endStream(streamId, 'canceled_by_user');
        return reader.cancel();
      }
    };
  }, [logChunk, endStream]);

  return {
    createTracedSSEFetch,
    logChunk,
    endStream,
    getStreamMetrics,
    createSSEReader,
    activeStreams: activeStreams.current
  };
}

/**
 * React hook для трассировки WebSocket соединений
 */
export function useWebSocketTracing() {
  const adapter = useBrowserAdapter();
  const activeConnections = useRef<Map<string, WebSocketTraceContext>>(new Map());

  const createTracedWebSocket = useCallback((
    url: string,
    protocols?: string[],
    traceId?: string,
    parentSpanId?: string
  ) => {
    if (!TraceConfigBrowser.isTracingEnabled()) {
      const ws = new WebSocket(url, protocols);
      const mockContext: WebSocketTraceContext = {
        connectionId: `mock-${Date.now()}`,
        traceId: traceId || generateTraceId(),
        spanId: generateSpanId(),
        parentSpanId,
        url,
        protocols,
        startTime: Date.now()
      };
      return { ws, context: mockContext };
    }

    return adapter.trace('websocket_connection', () => {
      const result = chatTracer.createTracedWebSocket(url, protocols, traceId, parentSpanId);
      activeConnections.current.set(result.context.connectionId, result.context);
      return result;
    });
  }, [adapter]);

  const logMessage = useCallback((
    connectionId: string,
    direction: 'sent' | 'received',
    messageType: 'text' | 'binary' | 'ping' | 'pong' | 'close',
    size: number
  ) => {
    const context = activeConnections.current.get(connectionId);
    if (context) {
      chatTracer.logWebSocketMessage(context, direction, messageType, size);
    }
  }, []);

  const endConnection = useCallback((connectionId: string, closeCode?: number, closeReason?: string) => {
    const context = activeConnections.current.get(connectionId);
    if (context) {
      chatTracer.endWebSocketConnection(context, closeCode, closeReason);
      activeConnections.current.delete(connectionId);
    }
  }, []);

  return {
    createTracedWebSocket,
    logMessage,
    endConnection,
    activeConnections: activeConnections.current
  };
}

/**
 * React hook для трассировки UI событий
 */
export function useUITracing() {
  const adapter = useBrowserAdapter();

  const traceUIEvent = useCallback((eventType: string, elementId?: string, data?: any) => {
    if (!TraceConfigBrowser.isTracingEnabled()) return generateTraceId();

    return adapter.trace(`ui_${eventType.toLowerCase()}`, () => {
      const traceId = generateTraceId();
      const spanId = generateSpanId();

      // Создаем событие в JSON Lines формате
      // Получаем service info из конфигурации
      const serviceInfo = TraceConfigBrowser.getServiceInfo();
      
      const event = {
        [CanonicalFields.TIMESTAMP]: new Date().toISOString(),
        [CanonicalFields.TIMESTAMP_UNIX_NANOS]: (Date.now() * 1000000).toString(),
        [CanonicalFields.EVENT_ID]: generateTraceId(),
        [CanonicalFields.TRACE_ID]: traceId,
        [CanonicalFields.SPAN_ID]: spanId,
        [CanonicalFields.LEVEL]: 'INFO',
        [CanonicalFields.EVENT]: 'UI_EVENT',
        [CanonicalFields.EVENT_VERSION]: '1.0',
        [CanonicalFields.SCHEMA_VERSION]: 'filin.log.v1',
        [CanonicalFields.SERVICE_NAME]: serviceInfo.name,
        [CanonicalFields.SERVICE_VERSION]: serviceInfo.version,
        [CanonicalFields.SERVICE_NAMESPACE]: serviceInfo.namespace,
        [CanonicalFields.COMPONENT]: 'ui',
        [CanonicalFields.OPERATION]: eventType.toLowerCase(),
        [CanonicalFields.UI_ELEMENT_ID]: elementId || 'unknown',
        [CanonicalFields.UI_EVENT_TYPE]: eventType,
        [CanonicalFields.DATA_CLASSIFICATION]: 'internal'
      };

      adapter.logEvent(event as any);
      return traceId;
    });
  }, [adapter]);

  const traceClick = useCallback((elementId: string, data?: any) => {
    return traceUIEvent('CLICK', elementId, data);
  }, [traceUIEvent]);

  const traceInput = useCallback((elementId: string, value?: string) => {
    return traceUIEvent('INPUT', elementId, { valueLength: value?.length || 0 });
  }, [traceUIEvent]);

  return {
    traceUIEvent,
    traceClick,
    traceInput
  };
}



/**
 * Создает обертку для ClientApi с трассировкой
 */
export function createTracedClientApi(api: ClientApi, adapter?: TraceAdapter): ClientApi {
  if (!TraceConfigBrowser.isTracingEnabled()) {
    return api;
  }

  // Use provided adapter or create a new one
  const traceAdapter = adapter || new BrowserAdapter({
    telemetryEndpoint: '/api/telemetry',
    enableLocalStorage: true,
    maxStoredEvents: 1000
  });

  const tracedApi: ClientApi = { ...api };

  // Трассировка onApplyInEditor
  if (api.onApplyInEditor) {
    const originalApply = api.onApplyInEditor;
    tracedApi.onApplyInEditor = async (content: string) => {
      return traceAdapter.traceAsync('apply_in_editor', async () => {
        const traceId = generateTraceId();
        const { spanId } = chatTracer.startSpan('apply_in_editor', traceId);
        const startTime = Date.now();

        try {
          const result = await originalApply(content);
          const duration = Date.now() - startTime;

          // Проверяем на медленную операцию
          if (duration > chatTracer.getConfig().chatSlowMs) {
            chatTracer.logSlowOperation('apply_in_editor', duration, 'chat', traceId, spanId);
          }

          chatTracer.smartApply({ content }, true, duration, traceId, spanId);
          chatTracer.endSpan(traceId, spanId, undefined, duration, 'complete');
          return result;
        } catch (error) {
          const duration = Date.now() - startTime;
          chatTracer.smartApply({ content }, false, duration, traceId, spanId);
          chatTracer.endSpan(traceId, spanId, undefined, duration, 'error');
          throw error;
        }
      });
    };
  }

  // Трассировка onApplyInEditorV2 (Smart Apply)
  if (api.onApplyInEditorV2) {
    const originalApplyV2 = api.onApplyInEditorV2;
    tracedApi.onApplyInEditorV2 = async (content: string, options?: any) => {
      return traceAdapter.traceAsync('smart_apply_in_editor', async () => {
        const traceId = generateTraceId();
        const { spanId } = chatTracer.startSpan('smart_apply_in_editor', traceId);
        const startTime = Date.now();

        try {
          const result = await originalApplyV2(content, options);
          const duration = Date.now() - startTime;

          // Проверяем на медленную операцию
          if (duration > chatTracer.getConfig().chatSlowMs) {
            chatTracer.logSlowOperation('smart_apply_in_editor', duration, 'chat', traceId, spanId);
          }

          chatTracer.smartApply({ content, options }, true, duration, traceId, spanId);
          chatTracer.endSpan(traceId, spanId, undefined, duration, 'complete');
          return result;
        } catch (error) {
          const duration = Date.now() - startTime;
          chatTracer.smartApply({ content, options }, false, duration, traceId, spanId);
          chatTracer.endSpan(traceId, spanId, undefined, duration, 'error');
          throw error;
        }
      });
    };
  }

  // Трассировка openInEditor
  if (api.openInEditor) {
    const originalOpen = api.openInEditor;
    tracedApi.openInEditor = async (target) => {
      const traceId = generateTraceId();
      const { spanId } = chatTracer.startSpan('open_in_editor', traceId);
      const startTime = Date.now();

      try {
        const result = await originalOpen(target);
        const duration = Date.now() - startTime;
        chatTracer.endSpan(traceId, spanId, undefined, duration, 'complete');
        return result;
      } catch (error) {
        const duration = Date.now() - startTime;
        chatTracer.endSpan(traceId, spanId, undefined, duration, 'error');
        throw error;
      }
    };
  }

  // Трассировка getChanges для Generate Commit Message
  if (api.getChanges) {
    const originalGetChanges = api.getChanges;
    tracedApi.getChanges = async (params) => {
      const traceId = generateTraceId();
      const { spanId } = chatTracer.startSpan('get_changes', traceId);
      const startTime = Date.now();

      try {
        const result = await originalGetChanges(params);
        const duration = Date.now() - startTime;

        // Логируем получение изменений для коммита
        const event = chatTracer.createBaseEvent(
          'GIT_CHANGES_RETRIEVED',
          traceId,
          spanId,
          undefined
        );
        Object.assign(event, {
          [CanonicalFields.OPERATION]: 'get_git_changes',
          [CanonicalFields.GIT_CHANGES_COUNT]: result.length,
          [CanonicalFields.GIT_HAS_STAGED]: result.some(change => change.staged),
          [CanonicalFields.GIT_HAS_UNSTAGED]: result.some(change => !change.staged),
          [CanonicalFields.DURATION_MS]: duration
        });
        chatTracer.logEvent(event);

        chatTracer.endSpan(traceId, spanId, undefined, duration, 'complete');
        return result;
      } catch (error) {
        const duration = Date.now() - startTime;
        chatTracer.endSpan(traceId, spanId, undefined, duration, 'error');
        throw error;
      }
    };
  }

  return tracedApi;
}

/**
 * Создает обертку для ServerApi с трассировкой
 */
export function createTracedServerApi(api: ServerApi, adapter?: TraceAdapter): ServerApi {
  if (!TraceConfigBrowser.isTracingEnabled()) {
    return api;
  }

  // Use provided adapter or create a new one
  const traceAdapter = adapter || new BrowserAdapter({
    telemetryEndpoint: '/api/telemetry',
    enableLocalStorage: true,
    maxStoredEvents: 1000
  });

  const tracedApi: ServerApi = { ...api };

  // Трассировка executeCommand
  const originalExecuteCommand = api.executeCommand;
  tracedApi.executeCommand = async (command: ChatCommand) => {
    return traceAdapter.traceAsync('execute_chat_command', async () => {
      const traceId = generateTraceId();
      const { spanId } = chatTracer.startSpan('execute_chat_command', traceId);
      const startTime = Date.now();

      try {
        // Логируем выполнение команды
        chatTracer.chatCommand({
          type: ChatOperationType.CHAT_REQUEST,
          content: `Command: ${command}`,
          context: { command },
          traceId,
          spanId,
          parentSpanId: spanId
        }, traceId, spanId);

        const result = await originalExecuteCommand(command);
        const duration = Date.now() - startTime;

        // Проверяем на медленную операцию
        if (duration > chatTracer.getConfig().chatSlowMs) {
          chatTracer.logSlowOperation('execute_chat_command', duration, 'chat', traceId, spanId);
        }

        chatTracer.endSpan(traceId, spanId, undefined, duration, 'complete');
        return result;
      } catch (error) {
        const duration = Date.now() - startTime;
        chatTracer.endSpan(traceId, spanId, undefined, duration, 'error');
        throw error;
      }
    });
  };

  // Трассировка navigate
  if (api.navigate) {
    const originalNavigate = api.navigate;
    tracedApi.navigate = async (view) => {
      const traceId = generateTraceId();
      const { spanId } = chatTracer.startSpan('navigate_chat_view', traceId);
      const startTime = Date.now();

      try {
        const result = await originalNavigate(view);
        const duration = Date.now() - startTime;

        // Логируем навигацию
        const event = chatTracer.createBaseEvent(
          'CHAT_NAVIGATION',
          traceId,
          spanId,
          undefined
        );
        Object.assign(event, {
          [CanonicalFields.OPERATION]: 'navigate_chat_view',
          [CanonicalFields.CHAT_VIEW]: view,
          [CanonicalFields.DURATION_MS]: duration
        });
        chatTracer.logEvent(event);

        chatTracer.endSpan(traceId, spanId, undefined, duration, 'complete');
        return result;
      } catch (error) {
        const duration = Date.now() - startTime;
        chatTracer.endSpan(traceId, spanId, undefined, duration, 'error');
        throw error;
      }
    };
  }

  // Трассировка addRelevantContext
  if (api.addRelevantContext) {
    const originalAddContext = api.addRelevantContext;
    tracedApi.addRelevantContext = async (context) => {
      const traceId = generateTraceId();
      const { spanId } = chatTracer.startSpan('add_relevant_context', traceId);
      const startTime = Date.now();

      try {
        const result = await originalAddContext(context);
        const duration = Date.now() - startTime;

        // Логируем добавление контекста
        const event = chatTracer.createBaseEvent(
          'CHAT_CONTEXT_ADDED',
          traceId,
          spanId,
          undefined
        );
        const contextContent = context.kind === 'file' ? context.content : context.selection;
        Object.assign(event, {
          [CanonicalFields.OPERATION]: 'add_relevant_context',
          [CanonicalFields.CONTEXT_TYPE]: context.kind,
          [CanonicalFields.CONTEXT_HAS_CONTENT]: !!contextContent,
          [CanonicalFields.CONTEXT_CONTENT_LENGTH]: contextContent?.length || 0,
          [CanonicalFields.DURATION_MS]: duration
        });
        chatTracer.logEvent(event);

        chatTracer.endSpan(traceId, spanId, undefined, duration, 'complete');
        return result;
      } catch (error) {
        const duration = Date.now() - startTime;
        chatTracer.endSpan(traceId, spanId, undefined, duration, 'error');
        throw error;
      }
    };
  }

  // Трассировка updateActiveSelection
  if (api.updateActiveSelection) {
    const originalUpdateSelection = api.updateActiveSelection;
    tracedApi.updateActiveSelection = async (selection) => {
      const traceId = generateTraceId();
      const { spanId } = chatTracer.startSpan('update_active_selection', traceId);
      const startTime = Date.now();

      try {
        const result = await originalUpdateSelection(selection);
        const duration = Date.now() - startTime;

        // Логируем обновление выделения
        const event = chatTracer.createBaseEvent(
          'CHAT_SELECTION_UPDATED',
          traceId,
          spanId,
          undefined
        );
        const selectionContent = selection?.kind === 'file' ? selection.content : selection?.kind === 'terminal' ? selection.selection : undefined;
        Object.assign(event, {
          [CanonicalFields.OPERATION]: 'update_active_selection',
          [CanonicalFields.SELECTION_HAS_CONTENT]: !!selectionContent,
          [CanonicalFields.SELECTION_CONTENT_LENGTH]: selectionContent?.length || 0,
          [CanonicalFields.SELECTION_KIND]: selection?.kind || 'none',
          [CanonicalFields.DURATION_MS]: duration
        });
        chatTracer.logEvent(event);

        chatTracer.endSpan(traceId, spanId, undefined, duration, 'complete');
        return result;
      } catch (error) {
        const duration = Date.now() - startTime;
        chatTracer.endSpan(traceId, spanId, undefined, duration, 'error');
        throw error;
      }
    };
  }

  return tracedApi;
}