/**
 * Filin Tracer - Конфигурация трассировки (Табби чат панель)
 * 
 * Принцип: Использует базовую конфигурацию с переопределением для tabby-chat-panel
 */

import { TraceConfigManager, TraceConfig } from 'filin-base';

/**
 * Утилиты для работы с конфигурацией трассировки в tabby-chat-panel
 */
export class TraceConfigBrowser {

    /**
     * Проверяет включена ли трассировка
     * @return true если tracing enabled
     */
    static isTracingEnabled(): boolean {
        return TraceConfigManager.isTracingEnabled();
    }

    /**
     * Загружает конфигурацию по умолчанию с настройками для tabby-chat-panel
     * @return конфигурация TraceConfig для tabby-chat-panel
     */
    static loadDefaultConfig(): TraceConfig {
        const baseConfig = TraceConfigManager.loadDefaultConfig();

        // Переопределяем только специфичные для tabby-chat-panel настройки
        return {
            ...baseConfig,
            // OpenTelemetry Service Configuration для tabby-chat-panel
            otelService: {
                name: "tabby-chat-panel",
                version: "0.10.0",
                environment: "browser",
            },
            // Настройки для чата могут быть более медленными в браузере
            chatSlowMs: 5000,
            // В браузере операции могут занимать больше времени
            slowOperationMs: 7000,
        };
    }

    /**
     * Создает экземпляр DataMasker с конфигурацией
     * @param config конфигурация трассировки
     * @return экземпляр DataMasker
     */
    static createDataMasker(config: TraceConfig) {
        return TraceConfigManager.createDataMasker(config);
    }

    /**
     * Получает информацию о сервисе из конфигурации
     * @param config конфигурация трассировки (опционально)
     * @return объект с информацией о сервисе
     */
    static getServiceInfo(config?: TraceConfig) {
        const traceConfig = config || TraceConfigBrowser.loadDefaultConfig();
        return {
            name: traceConfig.otelService?.name || "tabby-chat-panel",
            version: traceConfig.otelService?.version || "0.10.0",
            instanceId: `browser-${Date.now()}`,
            namespace: "filin.chat",
            environment: traceConfig.otelService?.environment || "browser"
        };
    }

    /**
     * Создает конфигурацию транспортов на основе TraceConfig
     * @param traceConfig конфигурация трассировки
     * @return конфигурация для BrowserTransportManager
     */
    static createTransportConfig(traceConfig?: TraceConfig) {
        const config = traceConfig || TraceConfigBrowser.loadDefaultConfig();

        return {
            console: {
                enabled: config.enabled,
                format: 'json' as const,
                colorize: true
            },
            localStorage: {
                enabled: config.enabled,
                maxEvents: 1000,
                storageKey: 'filin-trace-events'
            },
            fetch: config.otlp?.endpoint ? {
                enabled: config.enabled,
                endpoint: config.otlp.endpoint.replace(':4317', '/api/telemetry'),
                headers: config.otlp.headers || {},
                method: 'POST' as const,
                batchSize: 50,
                batchTimeout: 10000,
                maxRetries: 3,
                retryDelay: 1000
            } : undefined,
            otlp: config.otlp?.endpoint ? {
                enabled: config.enabled,
                endpoint: config.otlp.endpoint,
                protocol: 'http' as const,
                headers: config.otlp.headers || {},
                batchSize: 50,
                batchTimeout: 10000,
                maxRetries: 3,
                retryDelay: 1000
            } : undefined,
            batchConfig: {
                size: 50,
                timeout: 10000,
                maxRetries: 3,
                retryDelay: 1000
            }
        };
    }
}

/**
 * Экспорт функций для удобства использования
 */
export const isTracingEnabledBrowser = TraceConfigBrowser.isTracingEnabled;
export const loadDefaultConfigBrowser = TraceConfigBrowser.loadDefaultConfig;
export const createDataMaskerBrowser = TraceConfigBrowser.createDataMasker;
export const createTransportConfigBrowser = TraceConfigBrowser.createTransportConfig;
export const getServiceInfoBrowser = TraceConfigBrowser.getServiceInfo;