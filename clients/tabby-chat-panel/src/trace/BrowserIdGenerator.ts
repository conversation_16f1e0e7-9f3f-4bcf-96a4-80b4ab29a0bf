/**
 * Браузерная реализация генератора ID согласно требованиям спецификации
 * 
 * Требования из .kiro/specs/filin-trace-comprehensive/requirements.md:
 * - event_id должен быть UUIDv7
 * - trace_id: 32 hex символа (UUIDv7 без дефисов)
 * - span_id: 16 hex символов (криптографически стойкий)
 * 
 * Браузерная версия использует Web Crypto API
 */

import { IdGenerator } from 'filin-base/browser';

export class BrowserIdGenerator implements IdGenerator {
  
  /**
   * Генерирует UUIDv7 (time-ordered UUID) для браузера
   * Формат: xxxxxxxx-xxxx-7xxx-yxxx-xxxxxxxxxxxx
   */
  private generateUUIDv7(): string {
    const timestamp = Date.now();
    
    // Генерируем 10 случайных байт
    const randomBytes = new Uint8Array(10);
    if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
      crypto.getRandomValues(randomBytes);
    } else {
      // Fallback для старых браузеров
      for (let i = 0; i < 10; i++) {
        randomBytes[i] = Math.floor(Math.random() * 256);
      }
    }
    
    // Timestamp (48 bits = 6 bytes)
    const timestampHex = timestamp.toString(16).padStart(12, '0');
    
    // Version (4 bits) + random (12 bits)
    const versionAndRandom = '7' + randomBytes[0].toString(16).padStart(3, '0').substring(0, 3);
    
    // Variant (2 bits) + random (62 bits)
    const variantByte = 0x80 | (randomBytes[1] & 0x3f);
    const variantAndRandom = variantByte.toString(16) + 
      Array.from(randomBytes.slice(2), b => b.toString(16).padStart(2, '0')).join('');
    
    // Форматируем как UUID
    return `${timestampHex.substring(0, 8)}-${timestampHex.substring(8, 12)}-${versionAndRandom}-${variantAndRandom.substring(0, 4)}-${variantAndRandom.substring(4, 16)}`;
  }
  
  /**
   * Генерирует trace ID используя UUIDv7 без дефисов
   */
  generateTraceId(): string {
    return this.generateUUIDv7().replace(/-/g, '');
  }
  
  /**
   * Генерирует span ID используя последние 8 байт от UUIDv7
   * Обеспечивает временную связь с trace ID для OpenTelemetry совместимости
   */
  generateSpanId(): string {
    // Генерируем UUIDv7 и берем последние 16 hex символов (8 байт)
    const uuid = this.generateUUIDv7().replace(/-/g, '');
    return uuid.substring(16, 32); // Последние 16 символов = 8 байт
  }
  
  /**
   * Генерирует event ID в формате UUIDv7
   */
  generateEventId(): string {
    return this.generateUUIDv7();
  }
}

/**
 * Детерминированный генератор для тестового режима в браузере
 */
export class DeterministicBrowserIdGenerator implements IdGenerator {
  private counter = 0;
  private baseTimestamp: number;
  
  constructor(baseTimestamp: number = 1736937000000) {
    this.baseTimestamp = baseTimestamp;
  }
  
  generateTraceId(): string {
    const timestamp = (this.baseTimestamp + this.counter * 1000).toString(16).padStart(12, '0');
    const counter = (++this.counter).toString(16).padStart(8, '0');
    const padding = '0'.repeat(12);
    return (timestamp + counter + padding).substring(0, 32);
  }
  
  generateSpanId(): string {
    const timestamp = (this.baseTimestamp + this.counter * 100).toString(16).padStart(8, '0');
    const counter = (++this.counter).toString(16).padStart(8, '0');
    return (timestamp + counter).substring(0, 16);
  }
  
  generateEventId(): string {
    const timestamp = (this.baseTimestamp + this.counter * 10).toString(16).padStart(12, '0');
    const counter = (++this.counter).toString(16).padStart(4, '0');
    
    return `${timestamp.substring(0, 8)}-${timestamp.substring(8, 12)}-7${counter.substring(0, 3)}-8000-000000000000`;
  }
}

/**
 * Инициализирует генератор ID для браузерного окружения
 * Должно вызываться при старте приложения
 */
export async function initializeBrowserIdGenerator(): Promise<void> {
  // Динамический импорт для избежания проблем с bundling
  const { setIdGenerator } = await import('filin-base');
  
  // Получаем информацию о тестовом режиме из конфигурации
  const { TraceConfigBrowser } = await import('./TraceConfigBrowser');
  const config = TraceConfigBrowser.loadDefaultConfig();
  const isTestMode = config.testMode;
  
  if (isTestMode) {
    console.log('Initializing deterministic ID generator for browser test mode');
    setIdGenerator(new DeterministicBrowserIdGenerator());
  } else {
    setIdGenerator(new BrowserIdGenerator());
  }
}