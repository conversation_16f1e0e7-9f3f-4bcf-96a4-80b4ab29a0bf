/**
 * Канонические утилиты трассировки для tabby-chat-panel
 * 
 * Использует каноническую схему именования полей и стандартную сериализацию
 * без кастомных функций переименования
 */

import { CanonicalFields } from "filin-base/browser";
import { 
  CanonicalTraceEvent, 
  CanonicalEventType, 
  CanonicalGenAIInfo,
  CanonicalChatMessageInfo,
  CanonicalStreamInfo,
  CanonicalWebSocketInfo
} from './CanonicalTypes';

// Экспортируем типы для использования в других файлах
export type { CanonicalGenAIInfo, CanonicalChatMessageInfo };
import { CanonicalSerializer, serializeCanonicalEvent } from './CanonicalSerializer';
import { TraceConfigBrowser } from './TraceConfigBrowser';
import { ChatPanelSchemaValidator } from './SchemaValidator';
import { TraceConfig } from 'filin-base/browser';

/**
 * Контекст спана с каноническими полями
 */
export interface CanonicalSpanContext {
  [CanonicalFields.TRACE_ID]: string;
  [CanonicalFields.SPAN_ID]: string;
  [CanonicalFields.PARENT_SPAN_ID]?: string;
}

/**
 * Контекст SSE стрима с каноническими полями
 */
export interface CanonicalSSEStreamContext extends CanonicalSpanContext {
  [CanonicalFields.STREAM_ID]: string;
  [CanonicalFields.TIMESTAMP]: number; // startTime
  [CanonicalFields.TIME_TO_FIRST_TOKEN]: number | undefined; // firstTokenTime
  [CanonicalFields.TIME_TO_LAST_BYTE]: number | undefined; // lastByteTime
  [CanonicalFields.STREAM_CHUNKS_TOTAL]: number; // chunkCount
  [CanonicalFields.BYTES_TOTAL]: number; // totalBytes
  [CanonicalFields.GEN_AI_USAGE_OUTPUT_TOKENS]: number; // totalTokens (инициализируется 0)
}

/**
 * Контекст WebSocket с каноническими полями
 */
export interface CanonicalWebSocketContext extends CanonicalSpanContext {
  [CanonicalFields.WEBSOCKET_CONNECTION_ID]: string;
  [CanonicalFields.WEBSOCKET_URL]: string;
  [CanonicalFields.WEBSOCKET_PROTOCOLS]?: string;
  [CanonicalFields.TIMESTAMP]: number; // startTime
}

/**
 * Канонические утилиты трассировки для чат-панели
 */
export class CanonicalTraceUtils {
  private config: TraceConfig;
  private serializer: CanonicalSerializer;
  private validator: ChatPanelSchemaValidator;
  private sessionId: string;
  private sessionSeq: number = 0;

  constructor(config: TraceConfig) {
    this.config = config;
    // В браузере используем development режим для лучшей отладки
    const isDevelopment = true; 
    this.serializer = isDevelopment 
      ? CanonicalSerializer.createDevelopmentSerializer()
      : CanonicalSerializer.createProductionSerializer();
    this.validator = isDevelopment
      ? ChatPanelSchemaValidator.createDevelopmentValidator()
      : ChatPanelSchemaValidator.createProductionValidator();
    this.sessionId = this.generateSessionId();
  }

  /**
   * Генерирует session_id для lifetime процесса
   */
  private generateSessionId(): string {
    return `chat-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Получает следующий sequence number для сессии
   */
  private getNextSessionSeq(): number {
    return ++this.sessionSeq;
  }

  /**
   * Генерирует UUIDv7 для event.id
   */
  private generateEventId(): string {
    const timestamp = Date.now();
    const timestampHex = timestamp.toString(16).padStart(12, '0');
    const randomHex = Math.random().toString(16).substr(2, 16).padStart(16, '0');
    
    return `${timestampHex.substr(0, 8)}-${timestampHex.substr(8, 4)}-7${randomHex.substr(0, 3)}-${randomHex.substr(3, 4)}-${randomHex.substr(7, 12)}`;
  }

  /**
   * Генерирует trace ID (32 hex lower-case)
   */
  private generateTraceId(): string {
    return Array.from({ length: 32 }, () => Math.floor(Math.random() * 16).toString(16)).join('');
  }

  /**
   * Генерирует span ID (16 hex lower-case)
   */
  private generateSpanId(): string {
    return Array.from({ length: 16 }, () => Math.floor(Math.random() * 16).toString(16)).join('');
  }

  /**
   * Создает базовое каноническое событие
   */
  private createBaseCanonicalEvent(
    eventType: CanonicalEventType,
    traceId: string,
    spanId: string,
    parentSpanId?: string
  ): CanonicalTraceEvent {
    const now = new Date();
    const tsUnixNanos = (now.getTime() * 1000000).toString();
    
    // Получаем service info из конфигурации
    const serviceInfo = TraceConfigBrowser.getServiceInfo();
    
    return {
      [CanonicalFields.TIMESTAMP]: now.toISOString(),
      [CanonicalFields.TIMESTAMP_UNIX_NANOS]: tsUnixNanos,
      [CanonicalFields.EVENT_ID]: this.generateEventId(),
      [CanonicalFields.TRACE_ID]: traceId,
      [CanonicalFields.SPAN_ID]: spanId,
      [CanonicalFields.PARENT_SPAN_ID]: parentSpanId,
      [CanonicalFields.LEVEL]: 'INFO',
      [CanonicalFields.EVENT]: eventType,
      [CanonicalFields.EVENT_VERSION]: '1.0',
      [CanonicalFields.SCHEMA_VERSION]: 'filin.log.v1',
      [CanonicalFields.SERVICE_NAME]: serviceInfo.name,
      [CanonicalFields.SERVICE_VERSION]: serviceInfo.version,
      [CanonicalFields.SERVICE_INSTANCE_ID]: serviceInfo.instanceId,
      [CanonicalFields.SERVICE_NAMESPACE]: serviceInfo.namespace,
      [CanonicalFields.DEPLOYMENT_ENVIRONMENT]: serviceInfo.environment,
      [CanonicalFields.COMPONENT]: 'chat-handler',
      [CanonicalFields.SESSION_ID]: this.sessionId,
      [CanonicalFields.IDE_SESSION_SEQ]: this.getNextSessionSeq(),
      [CanonicalFields.DATA_CLASSIFICATION]: 'internal'
    };
  }

  /**
   * Логирует каноническое событие
   */
  private logCanonicalEvent(event: CanonicalTraceEvent): void {
    if (!this.config.enabled) return;

    try {
      // Валидация события против схемы
      if (this.validator.shouldValidate()) {
        const validationResult = this.validator.validateSafe(event);
        
        if (!validationResult.valid) {
          console.error('[CanonicalTracer] Event validation failed:', validationResult.error);
          
          // В development режиме блокируем невалидные события (всегда в браузере)
          return;
        }
      }

      // Сериализация и логирование
      const jsonLine = this.serializer.serializeToJSONLines(event);
      console.log(jsonLine);
    } catch (error) {
      console.error('[CanonicalTracer] Failed to process event:', error);
    }
  }

  /**
   * Начинает спан с каноническими полями
   */
  startSpan(
    operation: string,
    traceId?: string,
    parentSpanId?: string
  ): CanonicalSpanContext {
    const actualTraceId = traceId || this.generateTraceId();
    const spanId = this.generateSpanId();

    if (this.config.enabled) {
      const event = this.createBaseCanonicalEvent(
        'SPAN_START',
        actualTraceId,
        spanId,
        parentSpanId
      );
      
      event[CanonicalFields.OPERATION] = operation;
      this.logCanonicalEvent(event);
    }

    return {
      [CanonicalFields.TRACE_ID]: actualTraceId,
      [CanonicalFields.SPAN_ID]: spanId,
      [CanonicalFields.PARENT_SPAN_ID]: parentSpanId
    };
  }

  /**
   * Завершает спан с каноническими полями
   */
  endSpan(
    context: CanonicalSpanContext,
    duration: number,
    stopReason: 'complete' | 'canceled_by_user' | 'deadline_exceeded' | 'error' = 'complete'
  ): void {
    if (!this.config.enabled) return;

    const event = this.createBaseCanonicalEvent(
      'SPAN_END',
      context[CanonicalFields.TRACE_ID],
      context[CanonicalFields.SPAN_ID],
      context[CanonicalFields.PARENT_SPAN_ID]
    );

    event[CanonicalFields.DURATION_MS] = duration;
    event[CanonicalFields.STOP_REASON] = stopReason;

    this.logCanonicalEvent(event);
  }

  /**
   * Логирует отправку чат-сообщения с каноническими полями
   */
  chatMessageSent(
    messageInfo: CanonicalChatMessageInfo,
    genAIInfo?: CanonicalGenAIInfo,
    traceId?: string,
    parentSpanId?: string
  ): CanonicalSpanContext {
    const actualTraceId = traceId || this.generateTraceId();
    const spanId = this.generateSpanId();

    if (this.config.enabled) {
      const event = this.createBaseCanonicalEvent(
        'CHAT_MESSAGE_SENT',
        actualTraceId,
        spanId,
        parentSpanId
      );

      // Добавляем чат-специфичные поля с каноническими именами
      event[CanonicalFields.OPERATION] = 'chat_message';
      event[CanonicalFields.MESSAGE_ID] = messageInfo[CanonicalFields.MESSAGE_ID];
      event[CanonicalFields.MESSAGE_ROLE] = messageInfo[CanonicalFields.MESSAGE_ROLE];
      event[CanonicalFields.MESSAGE_CONTENT_LENGTH] = messageInfo[CanonicalFields.MESSAGE_CONTENT_LENGTH];
      
      if (messageInfo[CanonicalFields.MESSAGE_HASH]) {
        event[CanonicalFields.MESSAGE_HASH] = messageInfo[CanonicalFields.MESSAGE_HASH];
      }

      // Добавляем GenAI поля с каноническими именами
      if (genAIInfo) {
        if (genAIInfo[CanonicalFields.GEN_AI_PROVIDER_NAME]) {
          event[CanonicalFields.GEN_AI_PROVIDER_NAME] = genAIInfo[CanonicalFields.GEN_AI_PROVIDER_NAME];
        }
        if (genAIInfo[CanonicalFields.GEN_AI_REQUEST_MODEL]) {
          event[CanonicalFields.GEN_AI_REQUEST_MODEL] = genAIInfo[CanonicalFields.GEN_AI_REQUEST_MODEL];
        }
        if (genAIInfo[CanonicalFields.GEN_AI_USAGE_INPUT_TOKENS]) {
          event[CanonicalFields.GEN_AI_USAGE_INPUT_TOKENS] = genAIInfo[CanonicalFields.GEN_AI_USAGE_INPUT_TOKENS];
        }
        if (genAIInfo[CanonicalFields.GEN_AI_USAGE_OUTPUT_TOKENS]) {
          event[CanonicalFields.GEN_AI_USAGE_OUTPUT_TOKENS] = genAIInfo[CanonicalFields.GEN_AI_USAGE_OUTPUT_TOKENS];
        }
      }

      this.logCanonicalEvent(event);
    }

    return {
      [CanonicalFields.TRACE_ID]: actualTraceId,
      [CanonicalFields.SPAN_ID]: spanId,
      [CanonicalFields.PARENT_SPAN_ID]: parentSpanId
    };
  }

  /**
   * Логирует ответ чата с каноническими полями
   */
  chatResponseReceived(
    messageInfo: CanonicalChatMessageInfo,
    context: CanonicalSpanContext,
    duration: number,
    genAIInfo?: CanonicalGenAIInfo
  ): void {
    if (!this.config.enabled) return;

    const event = this.createBaseCanonicalEvent(
      'CHAT_RESPONSE',
      context[CanonicalFields.TRACE_ID],
      context[CanonicalFields.SPAN_ID],
      context[CanonicalFields.PARENT_SPAN_ID]
    );

    event[CanonicalFields.OPERATION] = 'chat_response';
    event[CanonicalFields.DURATION_MS] = duration;
    event[CanonicalFields.MESSAGE_ID] = messageInfo[CanonicalFields.MESSAGE_ID];
    event[CanonicalFields.MESSAGE_ROLE] = messageInfo[CanonicalFields.MESSAGE_ROLE];
    event[CanonicalFields.MESSAGE_CONTENT_LENGTH] = messageInfo[CanonicalFields.MESSAGE_CONTENT_LENGTH];
    event[CanonicalFields.STOP_REASON] = 'complete';

    if (messageInfo[CanonicalFields.MESSAGE_HASH]) {
      event[CanonicalFields.MESSAGE_HASH] = messageInfo[CanonicalFields.MESSAGE_HASH];
    }

    // Добавляем GenAI поля с каноническими именами
    if (genAIInfo) {
      if (genAIInfo[CanonicalFields.GEN_AI_PROVIDER_NAME]) {
        event[CanonicalFields.GEN_AI_PROVIDER_NAME] = genAIInfo[CanonicalFields.GEN_AI_PROVIDER_NAME];
      }
      if (genAIInfo[CanonicalFields.GEN_AI_REQUEST_MODEL]) {
        event[CanonicalFields.GEN_AI_REQUEST_MODEL] = genAIInfo[CanonicalFields.GEN_AI_REQUEST_MODEL];
      }
      if (genAIInfo[CanonicalFields.GEN_AI_USAGE_INPUT_TOKENS]) {
        event[CanonicalFields.GEN_AI_USAGE_INPUT_TOKENS] = genAIInfo[CanonicalFields.GEN_AI_USAGE_INPUT_TOKENS];
      }
      if (genAIInfo[CanonicalFields.GEN_AI_USAGE_OUTPUT_TOKENS]) {
        event[CanonicalFields.GEN_AI_USAGE_OUTPUT_TOKENS] = genAIInfo[CanonicalFields.GEN_AI_USAGE_OUTPUT_TOKENS];
      }
    }

    this.logCanonicalEvent(event);
  }

  /**
   * Начинает SSE стрим с каноническими полями
   */
  startSSEStream(
    streamId: string,
    genAIInfo?: CanonicalGenAIInfo,
    traceId?: string,
    parentSpanId?: string
  ): CanonicalSSEStreamContext {
    const actualTraceId = traceId || this.generateTraceId();
    const spanId = this.generateSpanId();
    const startTime = Date.now();

    if (this.config.enabled) {
      const event = this.createBaseCanonicalEvent(
        'CHAT_STREAM_START',
        actualTraceId,
        spanId,
        parentSpanId
      );

      event[CanonicalFields.OPERATION] = 'chat_stream';
      event[CanonicalFields.STREAM_ID] = streamId;

      // Добавляем GenAI поля с каноническими именами
      if (genAIInfo?.[CanonicalFields.GEN_AI_REQUEST_MODEL]) {
        event[CanonicalFields.GEN_AI_REQUEST_MODEL] = genAIInfo[CanonicalFields.GEN_AI_REQUEST_MODEL];
      }
      if (genAIInfo?.[CanonicalFields.GEN_AI_PROVIDER_NAME]) {
        event[CanonicalFields.GEN_AI_PROVIDER_NAME] = genAIInfo[CanonicalFields.GEN_AI_PROVIDER_NAME];
      }

      this.logCanonicalEvent(event);
    }

    return {
      [CanonicalFields.TRACE_ID]: actualTraceId,
      [CanonicalFields.SPAN_ID]: spanId,
      [CanonicalFields.PARENT_SPAN_ID]: parentSpanId,
      [CanonicalFields.STREAM_ID]: streamId,
      [CanonicalFields.TIMESTAMP]: startTime,
      [CanonicalFields.TIME_TO_FIRST_TOKEN]: undefined,
      [CanonicalFields.TIME_TO_LAST_BYTE]: undefined,
      [CanonicalFields.STREAM_CHUNKS_TOTAL]: 0,
      [CanonicalFields.BYTES_TOTAL]: 0,
      [CanonicalFields.GEN_AI_USAGE_OUTPUT_TOKENS]: 0
    };
  }

  /**
   * Логирует чанк SSE стрима с каноническими полями
   */
  logSSEChunk(
    context: CanonicalSSEStreamContext,
    chunkData: { content?: string; tokens?: number },
    isFirstToken: boolean = false
  ): void {
    if (!this.config.enabled) return;

    context[CanonicalFields.STREAM_CHUNKS_TOTAL]++;
    if (chunkData.content) {
      context[CanonicalFields.BYTES_TOTAL] += new TextEncoder().encode(chunkData.content).length;
    }
    if (chunkData.tokens) {
      context[CanonicalFields.GEN_AI_USAGE_OUTPUT_TOKENS] += chunkData.tokens;
    }

    const now = Date.now();
    if (isFirstToken && (context[CanonicalFields.TIME_TO_FIRST_TOKEN] === undefined)) {
      context[CanonicalFields.TIME_TO_FIRST_TOKEN] = now;
    }
    context[CanonicalFields.TIME_TO_LAST_BYTE] = now;

    // Логируем каждый N-й чанк (используем значение из конфигурации)
    const logEveryN = this.config.streaming?.logEveryNChunks || 10;
    if (context[CanonicalFields.STREAM_CHUNKS_TOTAL] === 1 || context[CanonicalFields.STREAM_CHUNKS_TOTAL] % logEveryN === 0) {
      const event = this.createBaseCanonicalEvent(
        'CHAT_STREAM_CHUNK',
        context[CanonicalFields.TRACE_ID],
        context[CanonicalFields.SPAN_ID],
        context[CanonicalFields.PARENT_SPAN_ID]
      );

      event[CanonicalFields.OPERATION] = 'chat_stream_chunk';
      event[CanonicalFields.STREAM_ID] = context[CanonicalFields.STREAM_ID];
      event[CanonicalFields.STREAM_SEQ] = context[CanonicalFields.STREAM_CHUNKS_TOTAL];
      event[CanonicalFields.CHUNK_INDEX] = context[CanonicalFields.STREAM_CHUNKS_TOTAL];

      if (isFirstToken && context[CanonicalFields.TIME_TO_FIRST_TOKEN] !== undefined) {
        event[CanonicalFields.TIME_TO_FIRST_TOKEN] = context[CanonicalFields.TIME_TO_FIRST_TOKEN]! - context[CanonicalFields.TIMESTAMP];
      }

      this.logCanonicalEvent(event);
    }
  }

  /**
   * Завершает SSE стрим с каноническими полями
   */
  endSSEStream(
    context: CanonicalSSEStreamContext,
    stopReason: 'complete' | 'canceled_by_user' | 'deadline_exceeded' | 'error' = 'complete'
  ): void {
    if (!this.config.enabled) return;

    const endTime = Date.now();
    const event = this.createBaseCanonicalEvent(
      'CHAT_STREAM_END',
      context[CanonicalFields.TRACE_ID],
      context[CanonicalFields.SPAN_ID],
      context[CanonicalFields.PARENT_SPAN_ID]
    );

    event[CanonicalFields.OPERATION] = 'chat_stream_end';
    event[CanonicalFields.STREAM_ID] = context[CanonicalFields.STREAM_ID];
    event[CanonicalFields.STREAM_CHUNKS_TOTAL] = context[CanonicalFields.STREAM_CHUNKS_TOTAL];
    event[CanonicalFields.BYTES_TOTAL] = context[CanonicalFields.BYTES_TOTAL];
    event[CanonicalFields.STOP_REASON] = stopReason;

    if (context[CanonicalFields.GEN_AI_USAGE_OUTPUT_TOKENS] > 0) {
      event[CanonicalFields.GEN_AI_USAGE_OUTPUT_TOKENS] = context[CanonicalFields.GEN_AI_USAGE_OUTPUT_TOKENS];
    }

    if (context[CanonicalFields.TIME_TO_FIRST_TOKEN] !== undefined) {
      event[CanonicalFields.TIME_TO_FIRST_TOKEN] = context[CanonicalFields.TIME_TO_FIRST_TOKEN]! - context[CanonicalFields.TIMESTAMP];
    }
    if (context[CanonicalFields.TIME_TO_LAST_BYTE] !== undefined) {
      event[CanonicalFields.TIME_TO_LAST_BYTE] = context[CanonicalFields.TIME_TO_LAST_BYTE]! - context[CanonicalFields.TIMESTAMP];
    }

    this.logCanonicalEvent(event);
  }

  /**
   * Начинает WebSocket соединение с каноническими полями
   */
  startWebSocketConnection(
    connectionId: string,
    url: string,
    protocols?: string[],
    traceId?: string,
    parentSpanId?: string
  ): CanonicalWebSocketContext {
    const actualTraceId = traceId || this.generateTraceId();
    const spanId = this.generateSpanId();
    const startTime = Date.now();

    if (this.config.enabled) {
      const event = this.createBaseCanonicalEvent(
        'WEBSOCKET_CONNECTION_START',
        actualTraceId,
        spanId,
        parentSpanId
      );

      event[CanonicalFields.OPERATION] = 'websocket_connection';
      event[CanonicalFields.WEBSOCKET_CONNECTION_ID] = connectionId;
      event[CanonicalFields.WEBSOCKET_URL] = url;
      
      if (protocols && protocols.length > 0) {
        event[CanonicalFields.WEBSOCKET_PROTOCOLS] = protocols.join(',');
      }

      this.logCanonicalEvent(event);
    }

    return {
      [CanonicalFields.TRACE_ID]: actualTraceId,
      [CanonicalFields.SPAN_ID]: spanId,
      [CanonicalFields.PARENT_SPAN_ID]: parentSpanId,
      [CanonicalFields.WEBSOCKET_CONNECTION_ID]: connectionId,
      [CanonicalFields.WEBSOCKET_URL]: url,
      [CanonicalFields.WEBSOCKET_PROTOCOLS]: protocols?.join(','),
      [CanonicalFields.TIMESTAMP]: startTime
    };
  }

  /**
   * Логирует сообщение WebSocket с каноническими полями
   */
  logWebSocketMessage(
    context: CanonicalWebSocketContext,
    direction: 'sent' | 'received',
    messageType: 'text' | 'binary' | 'ping' | 'pong' | 'close',
    size: number
  ): void {
    if (!this.config.enabled) return;

    const event = this.createBaseCanonicalEvent(
      'WEBSOCKET_MESSAGE',
      context[CanonicalFields.TRACE_ID],
      this.generateSpanId(),
      context[CanonicalFields.SPAN_ID]
    );

    event[CanonicalFields.OPERATION] = 'websocket_message';
    event[CanonicalFields.WEBSOCKET_CONNECTION_ID] = context[CanonicalFields.WEBSOCKET_CONNECTION_ID];
    event[CanonicalFields.WEBSOCKET_DIRECTION] = direction;
    event[CanonicalFields.WEBSOCKET_MESSAGE_TYPE] = messageType;
    event[CanonicalFields.WEBSOCKET_MESSAGE_SIZE] = size;

    this.logCanonicalEvent(event);
  }

  /**
   * Завершает WebSocket соединение с каноническими полями
   */
  endWebSocketConnection(
    context: CanonicalWebSocketContext,
    closeCode?: number,
    closeReason?: string
  ): void {
    if (!this.config.enabled) return;

    const endTime = Date.now();
    const event = this.createBaseCanonicalEvent(
      'WEBSOCKET_CONNECTION_END',
      context[CanonicalFields.TRACE_ID],
      context[CanonicalFields.SPAN_ID],
      context[CanonicalFields.PARENT_SPAN_ID]
    );

    event[CanonicalFields.OPERATION] = 'websocket_connection';
    event[CanonicalFields.WEBSOCKET_CONNECTION_ID] = context[CanonicalFields.WEBSOCKET_CONNECTION_ID];
    event[CanonicalFields.DURATION_MS] = endTime - context[CanonicalFields.TIMESTAMP];
    event[CanonicalFields.STOP_REASON] = closeCode === 1000 ? 'complete' : 'error';

    if (closeCode !== undefined) {
      event[CanonicalFields.WEBSOCKET_CLOSE_CODE] = closeCode;
    }
    if (closeReason) {
      event[CanonicalFields.WEBSOCKET_CLOSE_REASON] = closeReason;
    }

    this.logCanonicalEvent(event);
  }

  /**
   * Создает экземпляр с конфигурацией по умолчанию
   */
  static createDefault(): CanonicalTraceUtils {
    // Используем конфигурацию из TraceConfigBrowser вместо дублирования
    const config = TraceConfigBrowser.loadDefaultConfig();
    return new CanonicalTraceUtils(config);
  }
}

/**
 * Глобальный экземпляр канонического трассировщика
 */
export const canonicalChatTracer = CanonicalTraceUtils.createDefault();