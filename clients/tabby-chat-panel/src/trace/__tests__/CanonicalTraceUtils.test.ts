/**
 * Тесты для канонических утилит трассировки tabby-chat-panel
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { CanonicalTraceUtils } from '../CanonicalTraceUtils';
import type { TraceConfig } from 'filin-base/browser';
import { CanonicalFields } from 'filin-base/browser';

// Мокаем console.log для тестирования
const mockConsoleLog = vi.fn();
const mockConsoleError = vi.fn();

vi.stubGlobal('console', {
  log: mockConsoleLog,
  error: mockConsoleError,
  warn: vi.fn(),
  info: vi.fn()
});

describe('CanonicalTraceUtils', () => {
  let tracer: CanonicalTraceUtils;
  let config: TraceConfig;

  beforeEach(() => {
    config = {
      enabled: true,
      level: 'INFO',
      slowOperationMs: 5000,
      autocompleteSlowMs: 1000,
      chatSlowMs: 3000,
      httpTimeoutMs: 20000,
      lspTimeoutMs: 10000,
      operationHangMs: 15000,
      maskTokens: true,
      maskPatterns: [],
      testMode: true,
      streamFullLog: false,
      otlp: {
        endpoint: "http://localhost:4317",
        headers: {}
      },
      sampling: {
        ratio: 1.0,
        forceSampleOnError: true
      },
      streaming: {
        logEveryNChunks: 10,
        maxChunksLogged: 100
      }
    };

    tracer = new CanonicalTraceUtils(config);
    mockConsoleLog.mockClear();
    mockConsoleError.mockClear();
  });

  describe('startSpan', () => {
    it('should create span with canonical fields', () => {
      const context = tracer.startSpan('test_operation');

      expect(context[CanonicalFields.TRACE_ID]).toMatch(/^[a-f0-9]{32}$/);
      expect(context[CanonicalFields.SPAN_ID]).toMatch(/^[a-f0-9]{16}$/);
      expect(context[CanonicalFields.PARENT_SPAN_ID]).toBeUndefined();

      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining(`"${CanonicalFields.EVENT}":"SPAN_START"`)
      );
      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining(`"${CanonicalFields.OPERATION}":"test_operation"`)
      );
    });

    it('should use provided trace and parent span IDs', () => {
      const traceId = 'abcdef1234567890abcdef1234567890';
      const parentSpanId = 'abcdef1234567890';

      const context = tracer.startSpan('test_operation', traceId, parentSpanId);

      expect(context[CanonicalFields.TRACE_ID]).toBe(traceId);
      expect(context[CanonicalFields.PARENT_SPAN_ID]).toBe(parentSpanId);
    });
  });

  describe('endSpan', () => {
    it('should end span with canonical fields', () => {
      const context = tracer.startSpan('test_operation');
      mockConsoleLog.mockClear();

      tracer.endSpan(context, 1500, 'complete');

      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining(`"${CanonicalFields.EVENT}":"SPAN_END"`)
      );
      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining(`"${CanonicalFields.DURATION_MS}":1500`)
      );
      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining(`"${CanonicalFields.STOP_REASON}":"complete"`)
      );
    });
  });

  describe('chatMessageSent', () => {
    it('should log chat message with canonical GenAI fields', () => {
      const messageInfo = {
        [CanonicalFields.MESSAGE_ID]: 'msg-123',
        [CanonicalFields.MESSAGE_ROLE]: 'user' as const,
        [CanonicalFields.MESSAGE_CONTENT_LENGTH]: 50,
        [CanonicalFields.MESSAGE_HASH]: 'sha256:abc123...'
      };

      const genAIInfo = {
        [CanonicalFields.GEN_AI_PROVIDER_NAME]: 'openai',
        [CanonicalFields.GEN_AI_REQUEST_MODEL]: 'gpt-4',
        [CanonicalFields.GEN_AI_USAGE_INPUT_TOKENS]: 100
      };

      const context = tracer.chatMessageSent(messageInfo, genAIInfo);

      expect(context[CanonicalFields.TRACE_ID]).toMatch(/^[a-f0-9]{32}$/);
      expect(context[CanonicalFields.SPAN_ID]).toMatch(/^[a-f0-9]{16}$/);

      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining(`"${CanonicalFields.EVENT}":"CHAT_MESSAGE_SENT"`)
      );
      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining(`"${CanonicalFields.MESSAGE_ID}":"msg-123"`)
      );
      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining(`"${CanonicalFields.MESSAGE_ROLE}":"user"`)
      );
      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining(`"${CanonicalFields.GEN_AI_PROVIDER_NAME}":"openai"`)
      );
      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining(`"${CanonicalFields.GEN_AI_REQUEST_MODEL}":"gpt-4"`)
      );
      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining(`"${CanonicalFields.GEN_AI_USAGE_INPUT_TOKENS}":100`)
      );
    });

    it('should work without GenAI info', () => {
      const messageInfo = {
        [CanonicalFields.MESSAGE_ID]: 'msg-123',
        [CanonicalFields.MESSAGE_ROLE]: 'user' as const,
        [CanonicalFields.MESSAGE_CONTENT_LENGTH]: 50
      };

      const context = tracer.chatMessageSent(messageInfo);

      expect(context[CanonicalFields.TRACE_ID]).toMatch(/^[a-f0-9]{32}$/);
      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining(`"${CanonicalFields.MESSAGE_ID}":"msg-123"`)
      );
      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.not.stringContaining(`"${CanonicalFields.GEN_AI_REQUEST_MODEL}"`)
      );
    });
  });

  describe('chatResponseReceived', () => {
    it('should log chat response with canonical fields', () => {
      const context = tracer.startSpan('chat_operation');
      mockConsoleLog.mockClear();

      const messageInfo = {
        [CanonicalFields.MESSAGE_ID]: 'msg-456',
        [CanonicalFields.MESSAGE_ROLE]: 'assistant' as const,
        [CanonicalFields.MESSAGE_CONTENT_LENGTH]: 200,
        [CanonicalFields.MESSAGE_HASH]: 'sha256:def456...'
      };

      const genAIInfo = {
        [CanonicalFields.GEN_AI_REQUEST_MODEL]: 'gpt-4',
        [CanonicalFields.GEN_AI_USAGE_OUTPUT_TOKENS]: 150
      };

      tracer.chatResponseReceived(messageInfo, context, 2500, genAIInfo);

      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining(`"${CanonicalFields.EVENT}":"CHAT_RESPONSE"`)
      );
      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining(`"${CanonicalFields.DURATION_MS}":2500`)
      );
      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining(`"${CanonicalFields.MESSAGE_ROLE}":"assistant"`)
      );
      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining(`"${CanonicalFields.GEN_AI_USAGE_OUTPUT_TOKENS}":150`)
      );
    });
  });

  describe('SSE streaming', () => {
    it('should handle SSE stream lifecycle with canonical fields', () => {
      const genAIInfo = {
        [CanonicalFields.GEN_AI_REQUEST_MODEL]: 'gpt-4',
        [CanonicalFields.GEN_AI_PROVIDER_NAME]: 'openai'
      };

      // Start stream
      const streamContext = tracer.startSSEStream('stream-123', genAIInfo);

      expect(streamContext[CanonicalFields.STREAM_ID]).toBe('stream-123');
      expect(streamContext[CanonicalFields.TRACE_ID]).toMatch(/^[a-f0-9]{32}$/);
      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining(`"${CanonicalFields.EVENT}":"CHAT_STREAM_START"`)
      );
      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining(`"${CanonicalFields.STREAM_ID}":"stream-123"`)
      );
      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining(`"${CanonicalFields.GEN_AI_REQUEST_MODEL}":"gpt-4"`)
      );

      mockConsoleLog.mockClear();

      // Log chunk
      tracer.logSSEChunk(streamContext, { content: 'Hello', tokens: 1 }, true);

      expect(streamContext.chunkCount).toBe(1);
      expect(streamContext.totalBytes).toBeGreaterThan(0);
      expect(streamContext.firstTokenTime).toBeDefined();

      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining(`"${CanonicalFields.EVENT}":"CHAT_STREAM_CHUNK"`)
      );
      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining(`"${CanonicalFields.STREAM_SEQ}":1`)
      );
      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining(`"${CanonicalFields.TIME_TO_FIRST_TOKEN}"`)
      );

      mockConsoleLog.mockClear();

      // End stream
      tracer.endSSEStream(streamContext, 'complete');

      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining(`"${CanonicalFields.EVENT}":"CHAT_STREAM_END"`)
      );
      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining(`"${CanonicalFields.STREAM_CHUNKS_TOTAL}":1`)
      );
      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining(`"${CanonicalFields.STOP_REASON}":"complete"`)
      );
    });
  });

  describe('WebSocket tracing', () => {
    it('should handle WebSocket lifecycle with canonical fields', () => {
      // Start connection
      const wsContext = tracer.startWebSocketConnection(
        'ws-123',
        'wss://example.com/chat',
        ['chat-protocol']
      );

      expect(wsContext[CanonicalFields.WEBSOCKET_CONNECTION_ID]).toBe('ws-123');
      expect(wsContext[CanonicalFields.WEBSOCKET_URL]).toBe('wss://example.com/chat');
      expect(wsContext[CanonicalFields.WEBSOCKET_PROTOCOLS]).toBe('chat-protocol');

      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining(`"${CanonicalFields.EVENT}":"WEBSOCKET_CONNECTION_START"`)
      );
      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining(`"${CanonicalFields.WEBSOCKET_CONNECTION_ID}":"ws-123"`)
      );
      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining(`"${CanonicalFields.WEBSOCKET_URL}":"wss://example.com/chat"`)
      );

      mockConsoleLog.mockClear();

      // Log message
      tracer.logWebSocketMessage(wsContext, 'sent', 'text', 100);

      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining(`"${CanonicalFields.EVENT}":"WEBSOCKET_MESSAGE"`)
      );
      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining(`"${CanonicalFields.WEBSOCKET_DIRECTION}":"sent"`)
      );
      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining(`"${CanonicalFields.WEBSOCKET_MESSAGE_TYPE}":"text"`)
      );
      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining(`"${CanonicalFields.WEBSOCKET_MESSAGE_SIZE}":100`)
      );

      mockConsoleLog.mockClear();

      // End connection
      tracer.endWebSocketConnection(wsContext, 1000, 'Normal closure');

      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining(`"${CanonicalFields.EVENT}":"WEBSOCKET_CONNECTION_END"`)
      );
      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining(`"${CanonicalFields.WEBSOCKET_CLOSE_CODE}":1000`)
      );
      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining(`"${CanonicalFields.WEBSOCKET_CLOSE_REASON}":"Normal closure"`)
      );
      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining(`"${CanonicalFields.STOP_REASON}":"complete"`)
      );
    });
  });

  describe('disabled tracing', () => {
    it('should not log when tracing is disabled', () => {
      const disabledConfig = { ...config, enabled: false };
      const disabledTracer = new CanonicalTraceUtils(disabledConfig);

      const context = disabledTracer.startSpan('test_operation');
      disabledTracer.endSpan(context, 1000);

      expect(mockConsoleLog).not.toHaveBeenCalled();
    });
  });

  describe('createDefault', () => {
    it('should create tracer with default configuration', () => {
      // Set environment variable for test
      process.env.FILIN_TRACE_ENABLED = 'true';

      const defaultTracer = CanonicalTraceUtils.createDefault();

      expect(defaultTracer).toBeInstanceOf(CanonicalTraceUtils);

      // Clean up
      delete process.env.FILIN_TRACE_ENABLED;
    });
  });

  describe('canonical field validation', () => {
    it('should generate events with all canonical field names', () => {
      const messageInfo = {
        [CanonicalFields.MESSAGE_ID]: 'msg-123',
        [CanonicalFields.MESSAGE_ROLE]: 'user' as const,
        [CanonicalFields.MESSAGE_CONTENT_LENGTH]: 50
      };

      tracer.chatMessageSent(messageInfo);

      const loggedEvent = mockConsoleLog.mock.calls[0][0];
      const parsedEvent = JSON.parse(loggedEvent);

      // Verify all required canonical fields are present
      expect(parsedEvent).toHaveProperty(CanonicalFields.TIMESTAMP);
      expect(parsedEvent).toHaveProperty(CanonicalFields.TIMESTAMP_UNIX_NANOS);
      expect(parsedEvent).toHaveProperty(CanonicalFields.EVENT_ID);
      expect(parsedEvent).toHaveProperty(CanonicalFields.TRACE_ID);
      expect(parsedEvent).toHaveProperty(CanonicalFields.SPAN_ID);
      expect(parsedEvent).toHaveProperty(CanonicalFields.LEVEL);
      expect(parsedEvent).toHaveProperty(CanonicalFields.EVENT);
      expect(parsedEvent).toHaveProperty(CanonicalFields.EVENT_VERSION);
      expect(parsedEvent).toHaveProperty(CanonicalFields.SCHEMA_VERSION);
      expect(parsedEvent).toHaveProperty(CanonicalFields.SERVICE_NAME);
      expect(parsedEvent).toHaveProperty(CanonicalFields.SERVICE_VERSION);
      expect(parsedEvent).toHaveProperty(CanonicalFields.SERVICE_INSTANCE_ID);
      expect(parsedEvent).toHaveProperty(CanonicalFields.SERVICE_NAMESPACE);
      expect(parsedEvent).toHaveProperty(CanonicalFields.DEPLOYMENT_ENVIRONMENT);
      expect(parsedEvent).toHaveProperty(CanonicalFields.COMPONENT);

      // Verify no old field names are present
      expect(parsedEvent).not.toHaveProperty('traceId');
      expect(parsedEvent).not.toHaveProperty('spanId');
      expect(parsedEvent).not.toHaveProperty('eventId');
      expect(parsedEvent).not.toHaveProperty('model.name');
      expect(parsedEvent).not.toHaveProperty('prompt.tokens');
    });
  });
});