/**
 * API для сбора трейсовых данных из браузерной сессии
 * 
 * Позволяет внешним системам (IntelliJ плагин, скрипты) получать
 * трейсовые данные из браузерной сессии tabby-chat-panel для
 * сквозной трассировки.
 */

import { CanonicalFields } from 'filin-base/browser';
import type { TraceEvent } from 'filin-base/browser';
import { TraceConfigBrowser } from '../trace/TraceConfigBrowser';

export interface TraceCollectorAPI {
  /**
   * Получает все трейсы из текущей сессии
   */
  getAllTraces(): TraceEvent[];
  
  /**
   * Получает трейсы по trace_id для сквозной трассировки
   */
  getTracesByTraceId(traceId: string): TraceEvent[];
  
  /**
   * Получает трейсы за определенный период
   */
  getTracesByTimeRange(startTime: string, endTime: string): TraceEvent[];
  
  /**
   * Получает статистику трейсов
   */
  getTraceStats(): {
    totalEvents: number;
    uniqueTraceIds: string[];
    timeRange: { start: string; end: string };
    components: string[];
  };
  
  /**
   * Экспортирует трейсы в JSON Lines формате
   */
  exportTracesAsJSONL(): string;
  
  /**
   * Очищает трейсы (для тестирования)
   */
  clearTraces(): void;
}

/**
 * Реализация API сборщика трейсов
 */
export class BrowserTraceCollector implements TraceCollectorAPI {
  private adapter: any;
  
  constructor(adapter: any) {
    this.adapter = adapter;
  }
  
  getAllTraces(): TraceEvent[] {
    const transportManager = this.adapter.getTransportManager();
    const localStorageTransport = transportManager.getTransport('localStorage');
    
    if (localStorageTransport && 'getStoredEvents' in localStorageTransport) {
      return (localStorageTransport as any).getStoredEvents() || [];
    }
    
    return [];
  }
  
  getTracesByTraceId(traceId: string): TraceEvent[] {
    const allTraces = this.getAllTraces();
    return allTraces.filter(trace => trace['trace.id'] === traceId);
  }
  
  getTracesByTimeRange(startTime: string, endTime: string): TraceEvent[] {
    const allTraces = this.getAllTraces();
    const start = new Date(startTime).getTime();
    const end = new Date(endTime).getTime();
    
    return allTraces.filter(trace => {
      const traceTime = new Date(trace.ts).getTime();
      return traceTime >= start && traceTime <= end;
    });
  }
  
  getTraceStats() {
    const allTraces = this.getAllTraces();
    const uniqueTraceIds = [...new Set(allTraces.map(t => t['trace.id']))];
    const components = [...new Set(allTraces.map(t => t.component))];
    
    const timestamps = allTraces.map(t => new Date(t.ts).getTime());
    const timeRange = timestamps.length > 0 ? {
      start: new Date(Math.min(...timestamps)).toISOString(),
      end: new Date(Math.max(...timestamps)).toISOString()
    } : { start: '', end: '' };
    
    return {
      totalEvents: allTraces.length,
      uniqueTraceIds,
      timeRange,
      components
    };
  }
  
  exportTracesAsJSONL(): string {
    const allTraces = this.getAllTraces();
    return allTraces.map(trace => JSON.stringify(trace)).join('\n');
  }
  
  clearTraces(): void {
    const transportManager = this.adapter.getTransportManager();
    const localStorageTransport = transportManager.getTransport('localStorage');
    
    if (localStorageTransport && 'clearStoredEvents' in localStorageTransport) {
      (localStorageTransport as any).clearStoredEvents();
    }
  }
}

/**
 * HTTP API endpoints для внешнего доступа
 */
export class TraceCollectorHTTPAPI {
  private collector: BrowserTraceCollector;
  
  constructor(adapter: any) {
    this.collector = new BrowserTraceCollector(adapter);
  }
  
  /**
   * Настраивает HTTP endpoints для сбора трейсов
   * Может быть вызвано из IntelliJ плагина или других внешних систем
   */
  setupEndpoints() {
    // Создаем глобальный объект для доступа к API
    (window as any).filinTraceCollector = {
      getAllTraces: () => this.collector.getAllTraces(),
      getTracesByTraceId: (traceId: string) => this.collector.getTracesByTraceId(traceId),
      getTracesByTimeRange: (start: string, end: string) => this.collector.getTracesByTimeRange(start, end),
      getTraceStats: () => this.collector.getTraceStats(),
      exportTracesAsJSONL: () => this.collector.exportTracesAsJSONL(),
      clearTraces: () => this.collector.clearTraces()
    };
    
    console.log('[TraceCollector] HTTP API endpoints configured');
    console.log('Available at: window.filinTraceCollector');
  }
  
  /**
   * Создает REST-like API через postMessage для межоконного взаимодействия
   */
  setupPostMessageAPI() {
    window.addEventListener('message', (event) => {
      if (event.data.type !== 'FILIN_TRACE_REQUEST') {
        return;
      }
      
      const { method, params, requestId } = event.data;
      let result: any;
      
      try {
        switch (method) {
          case 'getAllTraces':
            result = this.collector.getAllTraces();
            break;
          case 'getTracesByTraceId':
            result = this.collector.getTracesByTraceId(params.traceId);
            break;
          case 'getTracesByTimeRange':
            result = this.collector.getTracesByTimeRange(params.startTime, params.endTime);
            break;
          case 'getTraceStats':
            result = this.collector.getTraceStats();
            break;
          case 'exportTracesAsJSONL':
            result = this.collector.exportTracesAsJSONL();
            break;
          case 'clearTraces':
            this.collector.clearTraces();
            result = { success: true };
            break;
          default:
            throw new Error(`Unknown method: ${method}`);
        }
        
        // Отправляем ответ
        event.source?.postMessage({
          type: 'FILIN_TRACE_RESPONSE',
          requestId,
          success: true,
          data: result
        }, { targetOrigin: event.origin });
        
      } catch (error) {
        // Отправляем ошибку
        event.source?.postMessage({
          type: 'FILIN_TRACE_RESPONSE',
          requestId,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        }, { targetOrigin: event.origin });
      }
    });
    
    console.log('[TraceCollector] PostMessage API configured');
  }
}

/**
 * Утилиты для интеграции с IntelliJ плагином
 */
export class IntelliJTraceIntegration {
  private collector: BrowserTraceCollector;
  
  constructor(adapter: any) {
    this.collector = new BrowserTraceCollector(adapter);
  }
  
  /**
   * Создает специальный endpoint для IntelliJ плагина
   * IntelliJ может вызывать JavaScript в браузере через WebView
   */
  setupIntelliJIntegration() {
    // Создаем специальный объект для IntelliJ
    (window as any).intellijTraceAPI = {
      /**
       * Получает трейсы для конкретной операции IntelliJ
       */
      getTracesForOperation: (operationId: string) => {
        const allTraces = this.collector.getAllTraces();
        return allTraces.filter(trace => 
          trace.operation === operationId || 
          (trace as any)['intellij.operation_id'] === operationId
        );
      },
      
      /**
       * Получает сквозные трейсы по trace_id из IntelliJ
       */
      getCrossSystemTraces: (traceId: string) => {
        return this.collector.getTracesByTraceId(traceId);
      },
      
      /**
       * Экспортирует трейсы в формате для анализа в IntelliJ
       */
      exportForIntelliJ: () => {
        const traces = this.collector.getAllTraces();
        const serviceInfo = TraceConfigBrowser.getServiceInfo();
        return {
          format: 'filin-trace-v1',
          exportedAt: new Date().toISOString(),
          source: serviceInfo.name,
          traces: traces
        };
      },
      
      /**
       * Получает метрики производительности для IntelliJ
       */
      getPerformanceMetrics: () => {
        const traces = this.collector.getAllTraces();
        const slowOperations = traces.filter(t => {
          const duration = t[CanonicalFields.DURATION_MS];
          return duration && duration > 1000;
        });
        const errors = traces.filter(t => t.level === 'ERROR');
        
        return {
          totalOperations: traces.length,
          slowOperations: slowOperations.length,
          errors: errors.length,
          avgDuration: traces
            .filter(t => t[CanonicalFields.DURATION_MS])
            .reduce((sum, t) => {
              const duration = t[CanonicalFields.DURATION_MS];
              return sum + (duration || 0);
            }, 0) / traces.length
        };
      }
    };
    
    console.log('[IntelliJ Integration] API configured');
    console.log('Available at: window.intellijTraceAPI');
  }
}

/**
 * Инициализация всех API для сбора трейсов
 */
export function initializeTraceCollectorAPIs(adapter: any) {
  // HTTP API
  const httpAPI = new TraceCollectorHTTPAPI(adapter);
  httpAPI.setupEndpoints();
  httpAPI.setupPostMessageAPI();
  
  // IntelliJ интеграция
  const intellijIntegration = new IntelliJTraceIntegration(adapter);
  intellijIntegration.setupIntelliJIntegration();
  
  console.log('[TraceCollector] All APIs initialized');
  console.log('Browser trace collection ready for cross-system tracing');
}