/**
 * Filin Tracer - Утилиты трассировки для VSCode расширения
 *
 * Принцип: Расширение базовых утилит для VSCode-специфичных операций
 * Интеграция: Использует filin-base для общей функциональности
 * Логирование: Интегрируется с VSCode LogOutputChannel и структурированным console.log
 */

import * as vscode from "vscode";
// Полностью независимая реализация для VSCode
import { TraceConfig, TraceEvent, TraceEventTypes } from "./types";
// Импорт правильных функций генерации ID из filin-base
import {
  generateTraceId,
  generateSpanId,
  generateEventId
} from "filin-base";

// Импорт утилит из filin-base
import {
  TraceUtilsBase,
  TraceConfigManager,
  DataMasker
} from "filin-base";
import { CanonicalFields } from "filin-base";

/**
 * W3C Trace Context interface (simplified for VSCode)
 */
export interface W3CTraceContext {
  traceparent: string;
  tracestate?: string;
  baggage?: string;
}

/**
 * Creates W3C traceparent header
 */
export function createTraceparent(traceId: string, spanId: string, flags = "01"): string {
  return `00-${traceId}-${spanId}-${flags}`;
}

/**
 * Creates W3C tracestate header
 */
export function createTracestate(data: Record<string, string>): string {
  const entries = Object.entries(data).map(([key, value]) => `${key}:${value}`);
  return `filin=${entries.join(";")}`;
}

/**
 * Creates W3C baggage header
 */
export function createBaggage(data: Record<string, string>) {
  return Object.entries(data)
    .map(([key, value]) => `${key}=${value}`)
    .join(",");
}

/**
 * Re-export generateSpanId from filin-base
 */
export { generateSpanId } from "filin-base";

/**
 * VSCode-специфичные типы операций
 */
export const VSCodeOperationType = {
  EXTENSION_ACTIVATE: "extension_activate",
  EXTENSION_DEACTIVATE: "extension_deactivate",
  COMMAND_EXECUTE: "command_execute",
  COMPLETION_PROVIDER: "completion_provider",
  DOCUMENT_CHANGE: "document_change",
  CONFIGURATION_CHANGE: "configuration_change",
  LSP_CLIENT_START: "lsp_client_start",
  LSP_CLIENT_STOP: "lsp_client_stop",
  WEBVIEW_CREATE: "webview_create",
  WEBVIEW_MESSAGE: "webview_message",
} as const;

export type VSCodeOperationTypeValue = (typeof VSCodeOperationType)[keyof typeof VSCodeOperationType];

/**
 * Утилиты трассировки для VSCode расширения
 * Расширяет базовые утилиты VSCode-специфичными методами
 */
import { VSCodeTransportManager } from "./VSCodeTransportManager";
import { schemaValidator } from "./SchemaValidator";

export class VSCodeTraceUtils extends TraceUtilsBase {
  public config: TraceConfig;
  protected dataMasker: DataMasker;
  private outputChannel: vscode.LogOutputChannel;
  private sessionId: string;
  private sessionSeq = 0;
  private sessionSeqLock = false;
  private transportManager: VSCodeTransportManager;

  constructor(configOrOutputChannel: TraceConfig | vscode.LogOutputChannel, outputChannel?: vscode.LogOutputChannel) {
    super();

    // Поддержка старого API: new TraceUtils(outputChannel)
    if (configOrOutputChannel && "info" in configOrOutputChannel && typeof configOrOutputChannel.info === "function") {
      this.outputChannel = configOrOutputChannel as vscode.LogOutputChannel;
      this.config = {
        enabled: TraceConfigManager.isTracingEnabled(),
        level: "INFO",
        slowOperationMs: 5000,
        httpTimeoutMs: 30000,
        lspTimeoutMs: 30000,
        operationHangMs: 10000,
        maskTokens: true,
        maskPatterns: [
          "Bearer\\s+[A-Za-z0-9\\-_]+",
          "token[\"']?\\s*[:=]\\s*[\"'][^\"']+[\"']",
          "api[_-]?key[\"']?\\s*[:=]\\s*[\"'][^\"']+[\"']",
          "password[\"']?\\s*[:=]\\s*[\"'][^\"']+[\"']",
          "secret[\"']?\\s*[:=]\\s*[\"'][^\"']+[\"']",
          "sk-[A-Za-z0-9]{48}",
        ],
        autocompleteSlowMs: 1000,
        chatSlowMs: 3000,
        testMode: false,
        streamFullLog: false,
        otlp: {
          endpoint: "http://localhost:4317",
          headers: {},
        },

        sampling: {
          ratio: 1.0,
          forceSampleOnError: true,
        },
        streaming: {
          logEveryNChunks: 10,
          maxChunksLogged: 100,
        },
      };
    } else {
      // Новый API: new TraceUtils(config, outputChannel)
      this.config = configOrOutputChannel as TraceConfig;
      if (!outputChannel) {
        throw new Error("OutputChannel is required when using config API");
      }
      this.outputChannel = outputChannel;
    }

    this.dataMasker = new DataMasker(this.config);
    this.sessionId = generateTraceId(); // IDE process lifetime

    // Initialize VSCode-specific transport manager
    this.transportManager = new VSCodeTransportManager({
      console: {
        enabled: true
      },
      file: {
        enabled: true,
        directory: '.filin/traces',
        filename: 'vscode-traces.jsonl'
      },
      otlp: this.config.otlp?.endpoint ? {
        enabled: true,
        endpoint: this.config.otlp.endpoint,
        headers: this.config.otlp.headers || {}
      } : { enabled: false }
    }, this.outputChannel);
  }

  /**
   * Логирует событие трассировки в VSCode Output Channel и структурированный console.log
   */
  public logEvent(eventType: string, operation: string, traceId: string, params?: Record<string, unknown>): void {
    if (!this.config.enabled) return;

    const timestamp = new Date().toISOString();
    const paramsStr = params
      ? Object.entries(params)
        .map(([key, value]) => `${key}=${value}`)
        .join(" ")
      : "";
    const paramsSection = paramsStr ? ` ${paramsStr}` : "";

    const message = `[FilinTracer] ${eventType} ${operation} trace_id=${traceId} timestamp=${timestamp}${paramsSection}`;

    // Используем VSCode LogOutputChannel для логирования
    this.outputChannel.info(message);
  }

  /**
   * Получает следующий монотонный sequence number (thread-safe)
   */
  private getNextSessionSeq(): number {
    // Простая реализация thread-safety для VSCode (single-threaded)
    while (this.sessionSeqLock) {
      // В VSCode нет реальной многопоточности, но добавляем для консистентности
    }
    this.sessionSeqLock = true;
    const seq = ++this.sessionSeq;
    this.sessionSeqLock = false;
    return seq;
  }

  /**
   * Создает базовый контекст для событий с общими полями
   */
  public createEventContext(baseFields: Partial<TraceEvent>): Partial<TraceEvent> {
    return { ...baseFields };
  }

  /**
   * Логирует структурированное событие с использованием контекста
   */
  public logStructuredEventWithContext(
    context: Partial<TraceEvent>,
    additionalFields: Partial<TraceEvent>
  ): void {
    const mergedEvent = { ...context, ...additionalFields };
    this.logStructuredEvent(mergedEvent);
  }

  /**
   * Логирует структурированное событие трассировки (JSON Lines)
   */
  public logStructuredEvent(event: Partial<TraceEvent>): void {
    if (!this.config.enabled) return;

    const now = new Date();
    const structuredEvent: TraceEvent = {
      [CanonicalFields.TIMESTAMP]: now.toISOString(),
      [CanonicalFields.TIMESTAMP_UNIX_NANOS]: (now.getTime() * 1000000).toString(),
      [CanonicalFields.EVENT_ID]: generateEventId(),
      [CanonicalFields.TRACE_ID]: event[CanonicalFields.TRACE_ID] || generateTraceId(),
      [CanonicalFields.SPAN_ID]: event[CanonicalFields.SPAN_ID] || generateSpanId(),
      [CanonicalFields.PARENT_SPAN_ID]: event[CanonicalFields.PARENT_SPAN_ID],
      [CanonicalFields.LEVEL]: event[CanonicalFields.LEVEL] || "INFO",
      [CanonicalFields.EVENT]: event[CanonicalFields.EVENT] || TraceEventTypes.SPAN_START,
      [CanonicalFields.EVENT_VERSION]: "1.0",
      [CanonicalFields.SCHEMA_VERSION]: "filin.log.v1",
      [CanonicalFields.SERVICE_NAME]: "vscode-extension",
      [CanonicalFields.SERVICE_VERSION]: vscode.extensions.getExtension("IT-One.vscode-filin")?.packageJSON?.version ?? "1.0.0",
      [CanonicalFields.SERVICE_INSTANCE_ID]: this.sessionId,
      [CanonicalFields.SERVICE_NAMESPACE]: "filin.ide",
      [CanonicalFields.DEPLOYMENT_ENVIRONMENT]: "dev",
      [CanonicalFields.COMPONENT]: event[CanonicalFields.COMPONENT] || "vscode",
      [CanonicalFields.OPERATION]: event[CanonicalFields.OPERATION],
      [CanonicalFields.DIRECTION]: event[CanonicalFields.DIRECTION],
      [CanonicalFields.SESSION_ID]: this.sessionId,
      [CanonicalFields.IDE_NAME]: "vscode",
      [CanonicalFields.IDE_VERSION]: vscode.version,
      [CanonicalFields.IDE_SESSION_SEQ]: this.getNextSessionSeq(),
      [CanonicalFields.DATA_CLASSIFICATION]: event[CanonicalFields.DATA_CLASSIFICATION] || "internal",
      ...event,
    };

    // Validate event against schema in development mode
    if (this.config.testMode || process.env["NODE_ENV"] === "development") {
      const validationResult = schemaValidator.validate(structuredEvent);
      if (!validationResult.valid && validationResult.errors) {
        const errorMessages = validationResult.errors.map(err => `${err.path}: ${err.message}`).join(", ");
        this.outputChannel.error(`Schema validation failed for event ${structuredEvent[CanonicalFields.EVENT_ID]}: ${errorMessages}`);

        // In test mode, throw error to fail fast
        if (this.config.testMode) {
          throw new Error(`Schema validation failed: ${errorMessages}`);
        }
      }
    }

    // Send to transport manager for multiple backends
    this.transportManager.send(structuredEvent);

    // Also log to VSCode Output Channel for development convenience
    this.outputChannel.info(
      `[FilinTracer] ${structuredEvent[CanonicalFields.EVENT]} ${structuredEvent[CanonicalFields.OPERATION] || ""} trace_id=${structuredEvent[CanonicalFields.TRACE_ID]}`,
    );
  }

  /**
   * Создает W3C Trace Context
   */
  public createTraceContext(traceId?: string, spanId?: string): W3CTraceContext {
    // Создаем контекст даже при выключенной трассировке, но он не будет использоваться
    const context: W3CTraceContext = {
      traceparent: createTraceparent(
        traceId || generateTraceId(),
        spanId || generateSpanId(),
        "01", // sampled
      ),
      tracestate: createTracestate({
        session: this.sessionId.substring(0, 8),
        workspace: this.getWorkspaceHash(),
        ide: "vscode",
      }),
      baggage: createBaggage({
        ide: "vscode",
        workspace: this.getWorkspaceHash(),
        session: this.sessionId.substring(0, 8),
      }),
    };

    return context;
  }

  /**
   * Извлекает trace context из LSP параметров
   */
  public extractTraceContextFromLspParams(params: unknown): W3CTraceContext | null {
    // Если трассировка выключена, не извлекаем trace context
    if (!this.config.enabled) {
      return null;
    }

    if (!params || typeof params !== "object" || !params) {
      return null;
    }

    const paramsObj = params as Record<string, unknown>;
    if (!paramsObj["x-filin-trace"]) {
      return null;
    }

    const filinTrace = paramsObj["x-filin-trace"] as Record<string, string>;
    if (!filinTrace?.["traceparent"]) {
      return null;
    }

    return {
      traceparent: filinTrace["traceparent"],
      tracestate: filinTrace["tracestate"],
      baggage: filinTrace["baggage"],
    };
  }

  /**
   * Добавляет trace context в LSP параметры
   */
  public addTraceContextToLspParams(params: unknown, context: W3CTraceContext): unknown {
    // Если трассировка выключена, возвращаем оригинальные параметры
    if (!this.config.enabled) {
      return params;
    }

    if (!params || typeof params !== "object") {
      return params;
    }

    return {
      ...params,
      "x-filin-trace": {
        traceparent: context.traceparent,
        tracestate: context.tracestate,
        baggage: context.baggage,
      },
    };
  }

  /**
   * Получает хеш workspace для идентификации
   */
  private getWorkspaceHash(): string {
    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (!workspaceFolders || workspaceFolders.length === 0) {
      return "no-workspace";
    }

    // Простой хеш на основе пути workspace
    const workspacePath = workspaceFolders[0]?.uri.fsPath || "unknown";
    let hash = 0;
    for (let i = 0; i < workspacePath.length; i++) {
      const char = workspacePath.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash).toString(16).substring(0, 8);
  }

  /**
   * Логирует активацию расширения
   * @param extensionId идентификатор расширения
   * @param traceId идентификатор трассировки (генерируется автоматически если не указан)
   * @return идентификатор трассировки и span_id
   */
  extensionActivate(extensionId: string, traceId?: string): { traceId: string; spanId: string } {
    const actualTraceId = traceId || generateTraceId();
    const spanId = generateSpanId();

    if (!this.config.enabled) return { traceId: actualTraceId, spanId };

    this.logStructuredEvent({
      [CanonicalFields.EVENT]: "SPAN_START",
      [CanonicalFields.TRACE_ID]: actualTraceId,
      [CanonicalFields.SPAN_ID]: spanId,
      [CanonicalFields.COMPONENT]: "extension",
      [CanonicalFields.OPERATION]: `extension_activate_${extensionId}`,
    });

    return { traceId: actualTraceId, spanId };
  }

  /**
   * Логирует начало деактивации расширения
   * @param extensionId идентификатор расширения
   * @return идентификатор трассировки и span_id
   */
  extensionDeactivate(extensionId: string): { traceId: string; spanId: string } {
    const traceId = generateTraceId();
    const spanId = generateSpanId();

    if (!this.config.enabled) return { traceId, spanId };

    this.logStructuredEvent({
      [CanonicalFields.EVENT]: "SPAN_START",
      [CanonicalFields.TRACE_ID]: traceId,
      [CanonicalFields.SPAN_ID]: spanId,
      [CanonicalFields.COMPONENT]: "extension",
      [CanonicalFields.OPERATION]: `extension_deactivate_${extensionId}`,
    });

    return { traceId, spanId };
  }

  /**
   * Логирует выполнение команды
   * @param commandId идентификатор команды
   * @param traceId идентификатор трассировки (генерируется автоматически если не указан)
   * @return идентификатор трассировки
   */
  commandExecute(commandId: string, traceId: string = generateTraceId()): string {
    if (!this.config.enabled) return traceId;

    const params = {
      commandId,
    };

    this.logEvent("COMMAND_EXECUTE", VSCodeOperationType.COMMAND_EXECUTE, traceId, params);
    return traceId;
  }

  /**
   * Логирует завершение выполнения команды
   * @param commandId идентификатор команды
   * @param traceId идентификатор трассировки
   * @param duration длительность выполнения в миллисекундах
   * @param success успешность выполнения
   */
  commandFinish(commandId: string, traceId: string, duration: number, success = true): void {
    if (!this.config.enabled) return;

    const params = {
      commandId,
      duration: `${duration}ms`,
      success,
    };

    this.logEvent("COMMAND_FINISH", VSCodeOperationType.COMMAND_EXECUTE, traceId, params);
  }

  /**
   * Логирует работу провайдера автодополнения
   * @param document документ
   * @param position позиция в документе
   * @param traceId идентификатор трассировки (генерируется автоматически если не указан)
   * @return идентификатор трассировки и span_id
   */
  completionProvider(
    document: vscode.TextDocument,
    _position: vscode.Position,
    traceId?: string,
  ): { traceId: string; spanId: string } {
    const actualTraceId = traceId || generateTraceId();
    const spanId = generateSpanId();

    if (!this.config.enabled) return { traceId: actualTraceId, spanId };

    this.logStructuredEvent({
      [CanonicalFields.EVENT]: "SPAN_START",
      [CanonicalFields.TRACE_ID]: actualTraceId,
      [CanonicalFields.SPAN_ID]: spanId,
      [CanonicalFields.COMPONENT]: "completion",
      [CanonicalFields.OPERATION]: `completion_${document.languageId}`,
      [CanonicalFields.RPC_SYSTEM]: "jsonrpc",
      [CanonicalFields.RPC_METHOD]: "textDocument/completion",
    });

    return { traceId: actualTraceId, spanId };
  }

  /**
   * Логирует завершение автодополнения
   */
  completionFinish(traceId: string, spanId: string, duration: number, success = true, stopReason = "complete"): void {
    if (!this.config.enabled) return;

    this.logStructuredEvent({
      [CanonicalFields.EVENT]: "SPAN_END",
      [CanonicalFields.TRACE_ID]: traceId,
      [CanonicalFields.SPAN_ID]: spanId,
      [CanonicalFields.COMPONENT]: "completion",
      [CanonicalFields.DURATION_MS]: duration,
      [CanonicalFields.STOP_REASON]: stopReason as "complete" | "canceled_by_user" | "deadline_exceeded" | "error",
      [CanonicalFields.LEVEL]: success ? "INFO" : "ERROR",
    });

    // Логируем медленные операции
    if (duration > this.config.slowOperationMs) {
      this.logStructuredEvent({
        [CanonicalFields.EVENT]: "SLOW_OPERATION",
        [CanonicalFields.TRACE_ID]: traceId,
        [CanonicalFields.SPAN_ID]: spanId,
        [CanonicalFields.COMPONENT]: "completion",
        [CanonicalFields.SLOW_KIND]: "autocomplete",
        [CanonicalFields.DURATION_MS]: duration,
        [CanonicalFields.LEVEL]: "WARN",
      });
    }
  }

  /**
   * Логирует изменение документа
   * @param document документ
   * @param changes изменения
   * @param traceId идентификатор трассировки (генерируется автоматически если не указан)
   * @return идентификатор трассировки
   */
  documentChange(
    document: vscode.TextDocument,
    changes: readonly vscode.TextDocumentContentChangeEvent[],
    traceId: string = generateTraceId(),
  ): string {
    if (!this.config.enabled) return traceId;

    const params = {
      languageId: document.languageId,
      fileName: document.fileName.split("/").pop() || "unknown",
      changesCount: changes.length,
      documentLength: document.getText().length,
    };

    this.logEvent("DOCUMENT_CHANGE", VSCodeOperationType.DOCUMENT_CHANGE, traceId, params);
    return traceId;
  }

  /**
   * Логирует изменение конфигурации
   * @param affectedKeys затронутые ключи конфигурации
   * @param traceId идентификатор трассировки (генерируется автоматически если не указан)
   * @return идентификатор трассировки
   */
  configurationChange(affectedKeys: string[], traceId: string = generateTraceId()): string {
    if (!this.config.enabled) return traceId;

    const params = {
      affectedKeys: affectedKeys.join(","),
      keysCount: affectedKeys.length,
    };

    this.logEvent("CONFIGURATION_CHANGE", VSCodeOperationType.CONFIGURATION_CHANGE, traceId, params);
    return traceId;
  }

  /**
   * Логирует запуск LSP клиента
   * @param serverName имя LSP сервера
   * @param traceId идентификатор трассировки (генерируется автоматически если не указан)
   * @return идентификатор трассировки
   */
  lspClientStart(serverName: string, traceId: string = generateTraceId()): string {
    if (!this.config.enabled) return traceId;

    const params = {
      serverName,
    };

    this.logEvent("LSP_CLIENT_START", VSCodeOperationType.LSP_CLIENT_START, traceId, params);
    return traceId;
  }

  /**
   * Логирует остановку LSP клиента
   * @param serverName имя LSP сервера
   * @param traceId идентификатор трассировки
   * @param duration длительность работы в миллисекундах
   */
  lspClientStop(serverName: string, traceId: string, duration: number): void {
    if (!this.config.enabled) return;

    const params = {
      serverName,
      duration: `${duration}ms`,
    };

    this.logEvent("LSP_CLIENT_STOP", VSCodeOperationType.LSP_CLIENT_STOP, traceId, params);
  }

  /**
   * Логирует LSP запрос
   * @param method метод LSP запроса
   * @param traceId идентификатор трассировки (генерируется автоматически если не указан)
   * @return идентификатор трассировки
   */
  lspRequest(method: string, traceId: string = generateTraceId()): string {
    if (!this.config.enabled) return traceId;

    const params = {
      method,
    };

    this.logEvent("LSP_REQUEST", "lsp_request", traceId, params);
    return traceId;
  }

  /**
   * Логирует LSP ответ
   * @param method метод LSP запроса
   * @param traceId идентификатор трассировки
   * @param duration длительность запроса в миллисекундах
   * @param success успешность запроса
   */
  lspResponse(method: string, traceId: string, duration: number, success = true): void {
    if (!this.config.enabled) return;

    const params = {
      method,
      duration: `${duration}ms`,
      success,
    };

    this.logEvent("LSP_RESPONSE", "lsp_response", traceId, params);
  }

  /**
   * Логирует создание webview
   * @param viewType тип webview
   * @param traceId идентификатор трассировки (генерируется автоматически если не указан)
   * @return идентификатор трассировки
   */
  webviewCreate(viewType: string, traceId: string = generateTraceId()): string {
    if (!this.config.enabled) return traceId;

    const params = {
      viewType,
    };

    this.logEvent("WEBVIEW_CREATE", VSCodeOperationType.WEBVIEW_CREATE, traceId, params);
    return traceId;
  }

  /**
   * Логирует сообщение webview
   * @param viewType тип webview
   * @param messageType тип сообщения
   * @param traceId идентификатор трассировки (генерируется автоматически если не указан)
   * @return идентификатор трассировки
   */
  webviewMessage(viewType: string, messageType: string, traceId: string = generateTraceId()): string {
    if (!this.config.enabled) return traceId;

    const params = {
      viewType,
      messageType,
    };

    this.logEvent("WEBVIEW_MESSAGE", VSCodeOperationType.WEBVIEW_MESSAGE, traceId, params);
    return traceId;
  }

  /**
   * Логирует начало чат-сессии
   */
  chatSessionStart(traceId?: string): { traceId: string; spanId: string } {
    const actualTraceId = traceId || generateTraceId();
    const spanId = generateSpanId();

    if (!this.config.enabled) return { traceId: actualTraceId, spanId };

    this.logStructuredEvent({
      [CanonicalFields.EVENT]: "SPAN_START",
      [CanonicalFields.TRACE_ID]: actualTraceId,
      [CanonicalFields.SPAN_ID]: spanId,
      [CanonicalFields.COMPONENT]: "chat",
      [CanonicalFields.OPERATION]: "chat_session",
    });

    return { traceId: actualTraceId, spanId };
  }

  /**
   * Логирует отправку чат-сообщения
   */
  chatMessageSent(traceId: string, parentSpanId: string, messageHash?: string): { traceId: string; spanId: string } {
    const spanId = generateSpanId();

    if (!this.config.enabled) return { traceId, spanId };

    this.logStructuredEvent({
      [CanonicalFields.EVENT]: "CHAT_MESSAGE_SENT",
      [CanonicalFields.TRACE_ID]: traceId,
      [CanonicalFields.SPAN_ID]: spanId,
      [CanonicalFields.PARENT_SPAN_ID]: parentSpanId,
      [CanonicalFields.COMPONENT]: "chat-ui",
      [CanonicalFields.OPERATION]: `chat_message_${messageHash ? "with_hash" : "no_hash"}`,
    });

    return { traceId, spanId };
  }

  /**
   * Логирует LSP запрос для чата
   */
  chatLspRequest(method: string, traceId: string, parentSpanId: string): { traceId: string; spanId: string } {
    const spanId = generateSpanId();

    if (!this.config.enabled) return { traceId, spanId };

    this.logStructuredEvent({
      [CanonicalFields.EVENT]: "LSP_REQUEST",
      [CanonicalFields.TRACE_ID]: traceId,
      [CanonicalFields.SPAN_ID]: spanId,
      [CanonicalFields.PARENT_SPAN_ID]: parentSpanId,
      [CanonicalFields.COMPONENT]: "lsp-client",
      [CanonicalFields.DIRECTION]: "sent",
      [CanonicalFields.RPC_SYSTEM]: "jsonrpc",
      [CanonicalFields.RPC_METHOD]: method,
    });

    return { traceId, spanId };
  }

  /**
   * Логирует LSP ответ для чата
   */
  chatLspResponse(method: string, traceId: string, spanId: string, duration: number, success = true): void {
    if (!this.config.enabled) return;

    this.logStructuredEvent({
      [CanonicalFields.EVENT]: "LSP_RESPONSE",
      [CanonicalFields.TRACE_ID]: traceId,
      [CanonicalFields.SPAN_ID]: spanId,
      [CanonicalFields.COMPONENT]: "lsp-client",
      [CanonicalFields.DIRECTION]: "received",
      [CanonicalFields.RPC_SYSTEM]: "jsonrpc",
      [CanonicalFields.RPC_METHOD]: method,
      [CanonicalFields.DURATION_MS]: duration,
      [CanonicalFields.LEVEL]: success ? "INFO" : "ERROR",
    });
  }

  /**
   * Логирует завершение чат-операции
   */
  chatOperationFinish(
    traceId: string,
    spanId: string,
    duration: number,
    success = true,
    stopReason = "complete",
  ): void {
    if (!this.config.enabled) return;

    this.logStructuredEvent({
      [CanonicalFields.EVENT]: "SPAN_END",
      [CanonicalFields.TRACE_ID]: traceId,
      [CanonicalFields.SPAN_ID]: spanId,
      [CanonicalFields.COMPONENT]: "chat",
      [CanonicalFields.DURATION_MS]: duration,
      [CanonicalFields.STOP_REASON]: stopReason as "complete" | "canceled_by_user" | "deadline_exceeded" | "error",
      [CanonicalFields.LEVEL]: success ? "INFO" : "ERROR",
    });

    // Логируем медленные чат-операции
    if (duration > this.config.chatSlowMs || duration > 30000) {
      this.logStructuredEvent({
        [CanonicalFields.EVENT]: "SLOW_OPERATION",
        [CanonicalFields.TRACE_ID]: traceId,
        [CanonicalFields.SPAN_ID]: spanId,
        [CanonicalFields.COMPONENT]: "chat",
        [CanonicalFields.SLOW_KIND]: "chat",
        [CanonicalFields.DURATION_MS]: duration,
        [CanonicalFields.LEVEL]: "WARN",
      });
    }
  }

  /**
   * Логирует таймаут чат-операции
   */
  chatTimeout(traceId: string, spanId: string, duration: number): void {
    if (!this.config.enabled) return;

    this.logStructuredEvent({
      [CanonicalFields.EVENT]: "LSP_TIMEOUT",
      [CanonicalFields.TRACE_ID]: traceId,
      [CanonicalFields.SPAN_ID]: spanId,
      [CanonicalFields.COMPONENT]: "chat",
      [CanonicalFields.DURATION_MS]: duration,
      [CanonicalFields.STOP_REASON]: "deadline_exceeded",
      [CanonicalFields.LEVEL]: "ERROR",
    });
  }

  /**
   * Логирует HTTP запрос с Idempotency-Key
   */
  httpRequest(
    method: string,
    url: string,
    traceId: string,
    parentSpanId?: string,
    idempotencyKey?: string,
  ): { traceId: string; spanId: string } {
    const spanId = generateSpanId();

    if (!this.config.enabled) return { traceId, spanId };

    // Создаем базовый контекст с общими полями
    const context = this.createEventContext({
      [CanonicalFields.TRACE_ID]: traceId,
      [CanonicalFields.SPAN_ID]: spanId,
      [CanonicalFields.PARENT_SPAN_ID]: parentSpanId,
      [CanonicalFields.COMPONENT]: "http-client",
      [CanonicalFields.HTTP_REQUEST_METHOD]: method,
      [CanonicalFields.URL_FULL]: url,
      [CanonicalFields.REQUEST_IDEMPOTENCY_KEY]: idempotencyKey,
    });

    // Логируем SPAN_START
    this.logStructuredEventWithContext(context, {
      [CanonicalFields.EVENT]: "SPAN_START",
      [CanonicalFields.OPERATION]: `http_${method.toLowerCase()}`,
    });

    // Логируем HTTP_REQUEST как span event
    this.logStructuredEventWithContext(context, {
      [CanonicalFields.EVENT]: "HTTP_REQUEST",
    });

    return { traceId, spanId };
  }

  /**
   * Логирует HTTP ответ
   */
  httpResponse(
    traceId: string,
    spanId: string,
    statusCode: number,
    duration: number,
    success = true,
    stopReason = "complete",
  ): void {
    if (!this.config.enabled) return;

    // Создаем базовый контекст для HTTP операций
    const baseContext = this.createEventContext({
      [CanonicalFields.TRACE_ID]: traceId,
      [CanonicalFields.SPAN_ID]: spanId,
      [CanonicalFields.COMPONENT]: "http-client",
      [CanonicalFields.HTTP_RESPONSE_STATUS_CODE]: statusCode,
      [CanonicalFields.LEVEL]: success ? "INFO" : "ERROR",
    });

    // Логируем HTTP_RESPONSE как span event
    this.logStructuredEventWithContext(baseContext, {
      [CanonicalFields.EVENT]: "HTTP_RESPONSE",
    });

    // Завершаем HTTP span с duration
    this.logStructuredEventWithContext(baseContext, {
      [CanonicalFields.EVENT]: "SPAN_END",
      [CanonicalFields.DURATION_MS]: duration,
      [CanonicalFields.STOP_REASON]: stopReason as "complete" | "canceled_by_user" | "deadline_exceeded" | "error",
    });

    // Логируем медленные HTTP операции
    if (duration > this.config.httpTimeoutMs) {
      this.logStructuredEventWithContext(baseContext, {
        [CanonicalFields.EVENT]: "HTTP_TIMEOUT",
        [CanonicalFields.DURATION_MS]: duration,
        [CanonicalFields.STOP_REASON]: "deadline_exceeded",
        [CanonicalFields.LEVEL]: "ERROR",
      });
    } else if (duration > this.config.slowOperationMs) {
      this.logStructuredEventWithContext(baseContext, {
        [CanonicalFields.EVENT]: "SLOW_OPERATION",
        [CanonicalFields.SLOW_KIND]: "http",
        [CanonicalFields.DURATION_MS]: duration,
        [CanonicalFields.LEVEL]: "WARN",
      });
    }
  }

  /**
   * Логирует зависание операции
   */
  hangDetected(
    traceId: string,
    spanId: string,
    operation: string,
    duration: number,
    hangKind = "unknown",
    stackTrace?: string,
  ): void {
    if (!this.config.enabled) return;

    this.logStructuredEvent({
      [CanonicalFields.EVENT]: "HANG_DETECTED",
      [CanonicalFields.TRACE_ID]: traceId,
      [CanonicalFields.SPAN_ID]: spanId,
      [CanonicalFields.COMPONENT]: "vscode",
      [CanonicalFields.OPERATION]: operation,
      [CanonicalFields.DURATION_MS]: duration,
      [CanonicalFields.HANG_KIND]: hangKind,
      [CanonicalFields.ERROR_STACK]: stackTrace,
      [CanonicalFields.LEVEL]: "ERROR",
    });
  }

  /**
   * Создает Idempotency-Key для операции
   */
  createIdempotencyKey(operation: string): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `${operation}-${timestamp}-${random}`;
  }

  /**
   * Выполняет VSCode операцию с автоматической трассировкой
   * @param operationType тип операции
   * @param operation функция операции
   * @param context контекст операции (опционально)
   * @return результат операции
   */
  async traceVSCodeOperation<T>(
    operationType: VSCodeOperationTypeValue,
    operation: (traceId: string) => Promise<T>,
    context?: unknown,
  ): Promise<T> {
    const traceId = generateTraceId();
    const startTime = Date.now();

    try {
      // Логируем начало операции
      const params = {
        operationType,
        hasContext: !!context,
        contextType: typeof context,
      };
      this.logEvent("VSCODE_OPERATION_START", operationType, traceId, params);

      // Выполняем операцию
      const result = await operation(traceId);

      // Логируем успешное завершение
      const duration = Date.now() - startTime;
      this.logEvent("VSCODE_OPERATION_FINISH", operationType, traceId, {
        duration: `${duration}ms`,
        success: true,
      });

      return result;
    } catch (error) {
      // Логируем ошибку
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      const maskedError = this.dataMasker.maskSensitiveData(errorMessage);

      this.logEvent("VSCODE_OPERATION_ERROR", operationType, traceId, {
        duration: `${duration}ms`,
        success: false,
        error: maskedError.maskedData,
      });

      throw error;
    }
  }

  /**
   * Начинает трассировку операции (для обратной совместимости)
   * @param operation название операции
   * @param traceId идентификатор трассировки (генерируется автоматически если не указан)
   * @return идентификатор трассировки
   */
  startTrace(operation: string, traceId: string = generateTraceId()): string {
    if (!this.config.enabled) return traceId;

    const params = { operation };
    this.logEvent("STARTED", operation, traceId, params);
    return traceId;
  }

  /**
   * Завершает трассировку операции (для обратной совместимости)
   * @param operation название операции
   * @param traceId идентификатор трассировки
   * @param duration длительность операции в миллисекундах
   */
  finishTrace(operation: string, traceId: string, duration: number): void {
    if (!this.config.enabled) return;

    const params = {
      operation,
      duration: `${duration}ms`,
    };

    if (duration > this.config.slowOperationMs) {
      this.logEvent("SLOW_OPERATION", operation, traceId, params);
    } else {
      this.logEvent("FINISHED", operation, traceId, params);
    }
  }

  /**
   * Логирует ошибку операции (для обратной совместимости)
   * @param operation название операции
   * @param traceId идентификатор трассировки
   * @param error описание ошибки
   */
  errorTrace(operation: string, traceId: string, error: string): void {
    if (!this.config.enabled) return;

    const maskedError = this.dataMasker.maskSensitiveData(error);
    const params = {
      operation,
      error: maskedError.maskedData,
    };

    this.logEvent("ERROR", operation, traceId, params);
  }

  /**
   * Выполняет синхронную функцию с автоматической трассировкой (для обратной совместимости)
   * @param operation название операции для трассировки
   * @param fn функция для выполнения
   * @return результат выполнения функции
   */
  trace<T>(operation: string, fn: () => T): T {
    const traceId = this.startTrace(operation);
    const startTime = Date.now();

    try {
      const result = fn();
      this.finishTrace(operation, traceId, Date.now() - startTime);
      return result;
    } catch (error) {
      this.errorTrace(operation, traceId, error instanceof Error ? error.message : String(error));
      throw error;
    }
  }

  /**
   * Выполняет асинхронную функцию с автоматической трассировкой (для обратной совместимости)
   * @param operation название операции для трассировки
   * @param fn асинхронная функция для выполнения
   * @return результат выполнения функции
   */
  async traceAsync<T>(operation: string, fn: () => Promise<T>): Promise<T> {
    const traceId = this.startTrace(operation);
    const startTime = Date.now();

    try {
      const result = await fn();
      this.finishTrace(operation, traceId, Date.now() - startTime);
      return result;
    } catch (error) {
      this.errorTrace(operation, traceId, error instanceof Error ? error.message : String(error));
      throw error;
    }
  }

  /**
   * Properly shutdown the tracer and flush all pending events
   */
  public async shutdown(): Promise<void> {
    if (this.transportManager) {
      await this.transportManager.close();
    }
  }

  /**
   * Создает экземпляр трассировщика с конфигурацией по умолчанию
   * @param outputChannel VSCode Output Channel для логирования
   * @return экземпляр VSCodeTraceUtils
   */
  static createDefault(outputChannel: vscode.LogOutputChannel): VSCodeTraceUtils {
    // Используем базовую конфигурацию из filin-base
    const config: TraceConfig = {
      enabled: TraceConfigManager.isTracingEnabled(),
      level: "INFO",
      slowOperationMs: 5000,
      httpTimeoutMs: 30000,
      lspTimeoutMs: 30000,
      operationHangMs: 10000,
      maskTokens: true,
      maskPatterns: [
        "Bearer\\s+[A-Za-z0-9\\-_]+",
        "token[\"']?\\s*[:=]\\s*[\"'][^\"']+[\"']",
        "api[_-]?key[\"']?\\s*[:=]\\s*[\"'][^\"']+[\"']",
        "password[\"']?\\s*[:=]\\s*[\"'][^\"']+[\"']",
        "secret[\"']?\\s*[:=]\\s*[\"'][^\"']+[\"']",
        "sk-[A-Za-z0-9]{48}",
      ],
      autocompleteSlowMs: 1000,
      chatSlowMs: 3000,
      testMode: false,
      streamFullLog: false,
      otlp: {
        endpoint: "http://localhost:4317",
        headers: {},
      },

      sampling: {
        ratio: 1.0,
        forceSampleOnError: true,
      },
      streaming: {
        logEveryNChunks: 10,
        maxChunksLogged: 100,
      },
    };

    return new VSCodeTraceUtils(config, outputChannel);
  }

  /**
   * Создает экземпляр трассировщика с конфигурацией из VSCode настроек
   * @param outputChannel VSCode Output Channel для логирования
   * @return экземпляр VSCodeTraceUtils
   */
  static createFromVSCodeSettings(outputChannel: vscode.LogOutputChannel): VSCodeTraceUtils {
    const workspaceConfig = vscode.workspace.getConfiguration("tabby");

    // Получаем настройки трассировки из VSCode конфигурации
    const config: TraceConfig = {
      enabled: TraceConfigManager.isTracingEnabled(),
      level:
        outputChannel.logLevel === vscode.LogLevel.Trace
          ? "TRACE"
          : outputChannel.logLevel === vscode.LogLevel.Debug
            ? "DEBUG"
            : "INFO",
      slowOperationMs: workspaceConfig.get("trace.slowOperationMs", 5000),
      httpTimeoutMs: workspaceConfig.get("trace.httpTimeoutMs", 30000),
      lspTimeoutMs: workspaceConfig.get("trace.lspTimeoutMs", 30000),
      operationHangMs: workspaceConfig.get("trace.operationHangMs", 10000),
      maskTokens: workspaceConfig.get("trace.maskTokens", true),
      maskPatterns: workspaceConfig.get("trace.maskPatterns", [
        "Bearer\\s+[A-Za-z0-9\\-_]+",
        "token[\"']?\\s*[:=]\\s*[\"'][^\"']+[\"']",
        "api[_-]?key[\"']?\\s*[:=]\\s*[\"'][^\"']+[\"']",
        "password[\"']?\\s*[:=]\\s*[\"'][^\"']+[\"']",
        "secret[\"']?\\s*[:=]\\s*[\"'][^\"']+[\"']",
        "sk-[A-Za-z0-9]{48}",
      ]),
      autocompleteSlowMs: workspaceConfig.get("trace.autocompleteSlowMs", 1000),
      chatSlowMs: workspaceConfig.get("trace.chatSlowMs", 3000),
      testMode: workspaceConfig.get("trace.testMode", false),
      streamFullLog: workspaceConfig.get("trace.streamFullLog", false),
      otlp: {
        endpoint: workspaceConfig.get("trace.otlp.endpoint", "http://localhost:4317"),
        headers: workspaceConfig.get("trace.otlp.headers", {}),
      },

      sampling: {
        ratio: workspaceConfig.get("trace.sampling.ratio", 1.0),
        forceSampleOnError: workspaceConfig.get("trace.sampling.forceSampleOnError", true),
      },
      streaming: {
        logEveryNChunks: workspaceConfig.get("trace.streaming.logEveryNChunks", 10),
        maxChunksLogged: workspaceConfig.get("trace.streaming.maxChunksLogged", 100),
      },
    };

    return new VSCodeTraceUtils(config, outputChannel);
  }

  /**
   * Обновляет конфигурацию трассировки из VSCode настроек
   */
  updateConfigFromVSCodeSettings(): void {
    const workspaceConfig = vscode.workspace.getConfiguration("tabby");

    this.config = {
      ...this.config,
      level:
        this.outputChannel.logLevel === vscode.LogLevel.Trace
          ? "TRACE"
          : this.outputChannel.logLevel === vscode.LogLevel.Debug
            ? "DEBUG"
            : "INFO",
      slowOperationMs: workspaceConfig.get("trace.slowOperationMs", this.config.slowOperationMs),
      httpTimeoutMs: workspaceConfig.get("trace.httpTimeoutMs", this.config.httpTimeoutMs),
      lspTimeoutMs: workspaceConfig.get("trace.lspTimeoutMs", this.config.lspTimeoutMs),
      operationHangMs: workspaceConfig.get("trace.operationHangMs", this.config.operationHangMs),
      maskTokens: workspaceConfig.get("trace.maskTokens", this.config.maskTokens),
      maskPatterns: workspaceConfig.get("trace.maskPatterns", this.config.maskPatterns),
    };

    // Обновляем DataMasker с новой конфигурацией
    this.dataMasker = new DataMasker(this.config);
  }

  /**
   * Логирует событие с использованием соответствующего уровня VSCode LogOutputChannel
   */
  public logEventWithLevel(
    level: "trace" | "debug" | "info" | "warn" | "error",
    eventType: string,
    operation: string,
    traceId: string,
    params?: Record<string, unknown>,
  ): void {
    if (!this.config.enabled) return;

    const timestamp = new Date().toISOString();
    const paramsStr = params
      ? Object.entries(params)
        .map(([key, value]) => `${key}=${value}`)
        .join(" ")
      : "";
    const paramsSection = paramsStr ? ` ${paramsStr}` : "";

    const message = `[FilinTracer] ${eventType} ${operation} trace_id=${traceId} timestamp=${timestamp}${paramsSection}`;

    // Используем соответствующий уровень логирования
    switch (level) {
      case "trace":
        this.outputChannel.trace(message);
        break;
      case "debug":
        this.outputChannel.debug(message);
        break;
      case "info":
        this.outputChannel.info(message);
        break;
      case "warn":
        this.outputChannel.warn(message);
        break;
      case "error":
        this.outputChannel.error(message);
        break;
    }
  }

  /**
   * Логирует ошибку с полным стеком вызовов
   * @param operation название операции
   * @param traceId идентификатор трассировки
   * @param error ошибка
   */
  logError(operation: string, traceId: string, error: Error): void {
    if (!this.config.enabled) return;

    const maskedMessage = this.dataMasker.maskSensitiveData(error.message);
    const maskedStack = error.stack
      ? this.dataMasker.maskSensitiveData(error.stack)
      : { maskedData: "No stack trace", maskingStats: { totalTokens: 0, maskedTokens: 0 } };

    const params = {
      operation,
      error: maskedMessage.maskedData,
      stack: maskedStack.maskedData,
      errorType: error.constructor.name,
    };

    this.logEventWithLevel("error", "ERROR", operation, traceId, params);
  }

  /**
   * Создает дочерний трассировщик для конкретного компонента
   * @param componentName имя компонента
   * @return новый экземпляр VSCodeTraceUtils с префиксом компонента
   */
  createChildTracer(componentName: string): VSCodeTraceUtils {
    const childOutputChannel = {
      ...this.outputChannel,
      info: (message: string, ...args: unknown[]) => this.outputChannel.info(`[${componentName}] ${message}`, ...args),
      debug: (message: string, ...args: unknown[]) =>
        this.outputChannel.debug(`[${componentName}] ${message}`, ...args),
      trace: (message: string, ...args: unknown[]) =>
        this.outputChannel.trace(`[${componentName}] ${message}`, ...args),
      warn: (message: string, ...args: unknown[]) => this.outputChannel.warn(`[${componentName}] ${message}`, ...args),
      error: (message: string, ...args: unknown[]) =>
        this.outputChannel.error(`[${componentName}] ${message}`, ...args),
    } as vscode.LogOutputChannel;

    return new VSCodeTraceUtils(this.config, childOutputChannel);
  }
}

/**
 * Экспорт основных функций для удобства использования
 */
export { generateTraceId, VSCodeOperationType as VSCodeOp };

// Экспорт для обратной совместимости
export { VSCodeTraceUtils as TraceUtils };
