/**
 * Filin Tracer - Конфигурация трассировки
 * 
 * Принцип: Централизованное управление конфигурацией для всех компонентов
 * Отделено от основных утилит для лучшей организации кода
 */

import { DataMasker } from './DataMasker';
import { TraceConfig, LogLevel } from './TraceTypes';

/**
 * Утилиты для работы с конфигурацией трассировки
 */
export class TraceConfigManager {
    static isTracingEnabled(): boolean {
        return true;
    }

    /**
     * Загружает конфигурацию по умолчанию с дефолтными значениями
     * @return базовая конфигурация TraceConfig
     */
    static loadDefaultConfig(): TraceConfig {
        return {
            enabled: TraceConfigManager.isTracingEnabled(),
            level: "INFO",
            slowOperationMs: 5000,
            httpTimeoutMs: 30000,
            lspTimeoutMs: 30000,
            operationHangMs: 10000,
            maskTokens: true,
            maskPatterns: [
                "Bearer\\s+[A-Za-z0-9\\-_]+",
                "token[\"']?\\s*[:=]\\s*[\"'][^\"']+[\"']",
                "api[_-]?key[\"']?\\s*[:=]\\s*[\"'][^\"']+[\"']",
                "password[\"']?\\s*[:=]\\s*[\"'][^\"']+[\"']",
                "secret[\"']?\\s*[:=]\\s*[\"'][^\"']+[\"']",
                "sk-[A-Za-z0-9]{48}",
            ],
            autocompleteSlowMs: 1000,
            chatSlowMs: 3000,
            testMode: false,
            streamFullLog: false,

            // Security and data classification
            dataClassification: "internal",

            // OTLP Configuration with Jaeger support
            otlp: {
                endpoint: "http://localhost:4318",
                tracesEndpoint: "http://localhost:4318/v1/traces",
                protocol: "http",
                headers: {},
            },

            // Jaeger Configuration
            jaeger: {
                uiPort: 16686,
                uiUrl: "http://localhost:16686",
                otlpGrpcPort: 4317,
                otlpHttpPort: 4318,
                samplingPort: 5778,
                zipkinPort: 9411,
            },

            // OpenTelemetry Service Configuration
            otelService: {
                name: "filin-tests",
                version: "1.0.0",
                environment: "test",
            },

            sampling: {
                ratio: 1.0,
                forceSampleOnError: true,
            },
            streaming: {
                logEveryNChunks: 10,
                maxChunksLogged: 100,
            },
        };
    }

    /**
     * Создает экземпляр DataMasker с конфигурацией
     * @param config конфигурация трассировки
     * @return экземпляр DataMasker
     */
    static createDataMasker(config: TraceConfig): DataMasker {
        return new DataMasker(config);
    }
}

/**
 * Экспорт функций для удобства использования
 */
export const isTracingEnabled = TraceConfigManager.isTracingEnabled;
export const loadDefaultConfig = TraceConfigManager.loadDefaultConfig;
export const createDataMasker = TraceConfigManager.createDataMasker;
