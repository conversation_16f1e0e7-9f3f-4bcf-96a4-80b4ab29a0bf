/**
 * Интеграция валидатора схемы для tabby-chat-panel
 * 
 * Обеспечивает валидацию событий трассировки против канонической JSON Schema
 * с поддержкой различных режимов валидации
 */

import { 
  UniversalValidator, 
  ValidationMode, 
  ValidationResult, 
  ValidationContext 
} from 'filin-base/schema';
import { CanonicalTraceEvent } from './CanonicalTypes';

/**
 * Конфигурация валидатора для чат-панели
 */
export interface ChatPanelValidatorConfig {
  mode: ValidationMode;
  samplingRate?: number;
  enableMetrics?: boolean;
  enableWarnings?: boolean;
  failFast?: boolean;
}

/**
 * Валидатор схемы для tabby-chat-panel
 */
export class ChatPanelSchemaValidator {
  private validator: UniversalValidator;
  private config: ChatPanelValidatorConfig;

  constructor(config: ChatPanelValidatorConfig) {
    this.config = config;
    
    const validationContext: ValidationContext = {
      mode: config.mode,
      samplingRate: config.samplingRate,
      enableMetrics: config.enableMetrics ?? true,
      enableWarnings: config.enableWarnings ?? true
    };

    this.validator = new UniversalValidator(validationContext);
  }

  /**
   * Валидирует каноническое событие трассировки
   */
  validate(event: CanonicalTraceEvent): ValidationResult {
    return this.validator.validate(event);
  }

  /**
   * Валидирует пакет событий
   */
  validateBatch(events: CanonicalTraceEvent[]): ValidationResult[] {
    return this.validator.validateBatch(events);
  }

  /**
   * Валидирует событие с обработкой ошибок
   */
  validateSafe(event: CanonicalTraceEvent): { valid: boolean; error?: string } {
    try {
      const result = this.validate(event);
      
      if (!result.valid && this.config.failFast) {
        const errorMessages = result.errors?.map(e => e.message).join('; ') || 'Unknown validation error';
        return { valid: false, error: errorMessages };
      }
      
      return { valid: result.valid };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Validation failed';
      
      if (this.config.failFast) {
        return { valid: false, error: errorMessage };
      }
      
      // В production режиме логируем ошибку, но не блокируем
      console.warn('[ChatPanelValidator] Validation error:', errorMessage);
      return { valid: true }; // Считаем валидным в случае ошибки валидатора
    }
  }

  /**
   * Проверяет, должно ли событие быть валидировано
   */
  shouldValidate(): boolean {
    return this.validator.shouldValidate();
  }

  /**
   * Получает метрики валидации
   */
  getMetrics() {
    return this.validator.getMetrics();
  }

  /**
   * Сбрасывает метрики валидации
   */
  resetMetrics(): void {
    this.validator.resetMetrics();
  }

  /**
   * Обновляет конфигурацию валидатора
   */
  updateConfig(config: Partial<ChatPanelValidatorConfig>): void {
    this.config = { ...this.config, ...config };
    
    this.validator.updateContext({
      mode: this.config.mode,
      samplingRate: this.config.samplingRate,
      enableMetrics: this.config.enableMetrics,
      enableWarnings: this.config.enableWarnings
    });
  }

  /**
   * Создает отчет о валидации для множественных событий
   */
  createValidationReport(events: CanonicalTraceEvent[]) {
    return this.validator.createValidationReport(events);
  }

  /**
   * Создает валидатор для development режима
   */
  static createDevelopmentValidator(): ChatPanelSchemaValidator {
    return new ChatPanelSchemaValidator({
      mode: ValidationMode.DEVELOPMENT,
      enableMetrics: true,
      enableWarnings: true,
      failFast: true
    });
  }

  /**
   * Создает валидатор для production режима
   */
  static createProductionValidator(samplingRate: number = 0.01): ChatPanelSchemaValidator {
    return new ChatPanelSchemaValidator({
      mode: ValidationMode.PRODUCTION,
      samplingRate,
      enableMetrics: true,
      enableWarnings: false,
      failFast: false
    });
  }

  /**
   * Создает валидатор для pipeline режима
   */
  static createPipelineValidator(): ChatPanelSchemaValidator {
    return new ChatPanelSchemaValidator({
      mode: ValidationMode.PIPELINE,
      enableMetrics: true,
      enableWarnings: true,
      failFast: true
    });
  }

  /**
   * Создает валидатор для тестирования
   */
  static createTestValidator(): ChatPanelSchemaValidator {
    return new ChatPanelSchemaValidator({
      mode: ValidationMode.DEVELOPMENT,
      enableMetrics: true,
      enableWarnings: true,
      failFast: true
    });
  }
}

/**
 * Глобальный экземпляр валидатора для удобства использования
 * В браузере всегда используем development validator для лучшей отладки
 */
export const chatPanelValidator = ChatPanelSchemaValidator.createDevelopmentValidator();

/**
 * Утилитарная функция для быстрой валидации события
 */
export function validateChatEvent(event: CanonicalTraceEvent): ValidationResult {
  return chatPanelValidator.validate(event);
}

/**
 * Утилитарная функция для безопасной валидации события
 */
export function validateChatEventSafe(event: CanonicalTraceEvent): { valid: boolean; error?: string } {
  return chatPanelValidator.validateSafe(event);
}