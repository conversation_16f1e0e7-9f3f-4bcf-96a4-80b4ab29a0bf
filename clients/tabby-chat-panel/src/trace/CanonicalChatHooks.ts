/**
 * Канонические React Hooks для Chat Panel
 * 
 * Использует каноническую схему именования полей и валидацию схемы
 * Фокус на GenAI полях для чат-функциональности
 */

import React, { useCallback, useRef, useState, useContext, createContext } from 'react';
import { TraceConfigBrowser } from './TraceConfigBrowser';
import { CanonicalFields } from "filin-base/browser";
import {
  canonicalChatTracer,
  type CanonicalSpanContext,
  type CanonicalSSEStreamContext,
  type CanonicalWebSocketContext,
  type CanonicalChatMessageInfo,
  type CanonicalGenAIInfo
} from './CanonicalTraceUtils';
import { BrowserAdapter, type TraceAdapter } from '../adapters/BrowserAdapter';
import type { ClientApi } from '../client';
import type { ServerApi, ChatCommand } from '../server';

// React Context для BrowserAdapter (переиспользуем из основных hooks)
const CanonicalBrowserAdapterContext = createContext<TraceAdapter | null>(null);

export function CanonicalBrowserAdapterProvider({ children }: { children: React.ReactNode }) {
  const [adapter] = useState(() => new BrowserAdapter({
    telemetryEndpoint: '/api/telemetry',
    enableLocalStorage: true,
    maxStoredEvents: 1000
  }));

  return React.createElement(CanonicalBrowserAdapterContext.Provider, { value: adapter }, children);
}

export function useCanonicalBrowserAdapter(): TraceAdapter {
  const adapter = useContext(CanonicalBrowserAdapterContext);
  if (!adapter) {
    throw new Error('useCanonicalBrowserAdapter must be used within CanonicalBrowserAdapterProvider');
  }
  return adapter;
}

/**
 * React hook для канонической трассировки чат-сообщений
 */
export function useCanonicalChatTracing() {
  const adapter = useCanonicalBrowserAdapter();
  const activeSpans = useRef<Map<string, CanonicalSpanContext>>(new Map());

  const traceMessage = useCallback((
    messageInfo: CanonicalChatMessageInfo,
    genAIInfo?: CanonicalGenAIInfo,
    traceId?: string,
    parentSpanId?: string
  ) => {
    if (!TraceConfigBrowser.isTracingEnabled()) {
      return {
        [CanonicalFields.TRACE_ID]: traceId || 'mock-trace-id',
        [CanonicalFields.SPAN_ID]: 'mock-span-id',
        [CanonicalFields.PARENT_SPAN_ID]: parentSpanId
      } as CanonicalSpanContext;
    }

    return adapter.trace('canonical_chat_message_sent', () => {
      const context = canonicalChatTracer.chatMessageSent(messageInfo, genAIInfo, traceId, parentSpanId);
      activeSpans.current.set(messageInfo[CanonicalFields.MESSAGE_ID], context);
      return context;
    });
  }, [adapter]);

  const traceResponse = useCallback((
    messageInfo: CanonicalChatMessageInfo,
    context: CanonicalSpanContext,
    duration: number,
    genAIInfo?: CanonicalGenAIInfo
  ) => {
    if (!TraceConfigBrowser.isTracingEnabled()) return;

    adapter.trace('canonical_chat_response_received', () => {
      canonicalChatTracer.chatResponseReceived(messageInfo, context, duration, genAIInfo);
    });
  }, [adapter]);

  const startSpan = useCallback((
    operation: string,
    traceId?: string,
    parentSpanId?: string
  ) => {
    if (!TraceConfigBrowser.isTracingEnabled()) {
      return {
        [CanonicalFields.TRACE_ID]: traceId || 'mock-trace-id',
        [CanonicalFields.SPAN_ID]: 'mock-span-id',
        [CanonicalFields.PARENT_SPAN_ID]: parentSpanId
      } as CanonicalSpanContext;
    }

    return adapter.trace(`canonical_${operation}`, () => {
      return canonicalChatTracer.startSpan(operation, traceId, parentSpanId);
    });
  }, [adapter]);

  const endSpan = useCallback((
    context: CanonicalSpanContext,
    duration: number,
    stopReason?: 'complete' | 'canceled_by_user' | 'deadline_exceeded' | 'error'
  ) => {
    if (!TraceConfigBrowser.isTracingEnabled()) return;

    canonicalChatTracer.endSpan(context, duration, stopReason);
  }, []);

  return {
    traceMessage,
    traceResponse,
    startSpan,
    endSpan,
    activeSpans: activeSpans.current
  };
}

/**
 * React hook для канонической трассировки SSE стриминга
 */
export function useCanonicalSSETracing() {
  const adapter = useCanonicalBrowserAdapter();
  const activeStreams = useRef<Map<string, CanonicalSSEStreamContext>>(new Map());

  const startStream = useCallback((
    streamId: string,
    genAIInfo?: CanonicalGenAIInfo,
    traceId?: string,
    parentSpanId?: string
  ) => {
    if (!TraceConfigBrowser.isTracingEnabled()) {
      return {
        [CanonicalFields.TRACE_ID]: traceId || 'mock-trace-id',
        [CanonicalFields.SPAN_ID]: 'mock-span-id',
        [CanonicalFields.PARENT_SPAN_ID]: parentSpanId,
        [CanonicalFields.STREAM_ID]: streamId,
        [CanonicalFields.TIMESTAMP]: Date.now(),
        [CanonicalFields.TIME_TO_FIRST_TOKEN]: undefined,
        [CanonicalFields.TIME_TO_LAST_BYTE]: undefined,
        [CanonicalFields.STREAM_CHUNKS_TOTAL]: 0,
        [CanonicalFields.BYTES_TOTAL]: 0,
        [CanonicalFields.GEN_AI_USAGE_OUTPUT_TOKENS]: 0
      } as CanonicalSSEStreamContext;
    }

    return adapter.trace('canonical_sse_stream_start', () => {
      const context = canonicalChatTracer.startSSEStream(streamId, genAIInfo, traceId, parentSpanId);
      activeStreams.current.set(streamId, context);
      return context;
    });
  }, [adapter]);

  const logChunk = useCallback((
    streamId: string,
    chunkData: { content?: string; tokens?: number },
    isFirstToken: boolean = false
  ) => {
    const context = activeStreams.current.get(streamId);
    if (context) {
      canonicalChatTracer.logSSEChunk(context, chunkData, isFirstToken);
    }
  }, []);

  const endStream = useCallback((
    streamId: string,
    stopReason?: 'complete' | 'canceled_by_user' | 'deadline_exceeded' | 'error'
  ) => {
    const context = activeStreams.current.get(streamId);
    if (context) {
      canonicalChatTracer.endSSEStream(context, stopReason);
      activeStreams.current.delete(streamId);
    }
  }, []);

  const getStreamMetrics = useCallback((streamId: string) => {
    const context = activeStreams.current.get(streamId);
    if (!context) return null;

    const now = Date.now();
    return {
      [CanonicalFields.STREAM_ID]: context[CanonicalFields.STREAM_ID],
      [CanonicalFields.TRACE_ID]: context[CanonicalFields.TRACE_ID],
      [CanonicalFields.SPAN_ID]: context[CanonicalFields.SPAN_ID],
      [CanonicalFields.STREAM_CHUNKS_TOTAL]: context[CanonicalFields.STREAM_CHUNKS_TOTAL],
      [CanonicalFields.BYTES_TOTAL]: context[CanonicalFields.BYTES_TOTAL],
      [CanonicalFields.GEN_AI_USAGE_OUTPUT_TOKENS]: context[CanonicalFields.GEN_AI_USAGE_OUTPUT_TOKENS],
      [CanonicalFields.TIME_TO_FIRST_TOKEN]: context[CanonicalFields.TIME_TO_FIRST_TOKEN] !== undefined ? context[CanonicalFields.TIME_TO_FIRST_TOKEN]! - context[CanonicalFields.TIMESTAMP] : undefined,
      [CanonicalFields.TIME_TO_LAST_BYTE]: context[CanonicalFields.TIME_TO_LAST_BYTE] !== undefined ? context[CanonicalFields.TIME_TO_LAST_BYTE]! - context[CanonicalFields.TIMESTAMP] : undefined,
      [CanonicalFields.DURATION_MS]: now - context[CanonicalFields.TIMESTAMP]
    };
  }, []);

  const createSSEReader = useCallback((response: Response, streamId: string) => {
    const context = activeStreams.current.get(streamId);
    if (!context || !response.body) return null;

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';

    return {
      async read() {
        const { done, value } = await reader.read();

        if (done) {
          endStream(streamId, 'complete');
          return { done: true, value: undefined };
        }

        const chunk = decoder.decode(value, { stream: true });
        buffer += chunk;

        // Обработка SSE событий
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') {
              endStream(streamId, 'complete');
              return { done: true, value: undefined };
            }

            try {
              const parsed = JSON.parse(data);
              const isFirstToken = context[CanonicalFields.STREAM_CHUNKS_TOTAL] === 0;

              logChunk(streamId, {
                content: parsed.choices?.[0]?.delta?.content || '',
                tokens: 1
              }, isFirstToken);

              return { done: false, value: parsed };
            } catch (error) {
              console.warn('Failed to parse SSE data:', error);
            }
          }
        }

        return { done: false, value: chunk };
      },

      cancel() {
        endStream(streamId, 'canceled_by_user');
        return reader.cancel();
      }
    };
  }, [logChunk, endStream]);

  return {
    startStream,
    logChunk,
    endStream,
    getStreamMetrics,
    createSSEReader,
    activeStreams: activeStreams.current
  };
}

/**
 * React hook для канонической трассировки WebSocket соединений
 */
export function useCanonicalWebSocketTracing() {
  const adapter = useCanonicalBrowserAdapter();
  const activeConnections = useRef<Map<string, CanonicalWebSocketContext>>(new Map());

  const startConnection = useCallback((
    connectionId: string,
    url: string,
    protocols?: string[],
    traceId?: string,
    parentSpanId?: string
  ) => {
    if (!TraceConfigBrowser.isTracingEnabled()) {
      return {
        [CanonicalFields.TRACE_ID]: traceId || 'mock-trace-id',
        [CanonicalFields.SPAN_ID]: 'mock-span-id',
        [CanonicalFields.PARENT_SPAN_ID]: parentSpanId,
        [CanonicalFields.WEBSOCKET_CONNECTION_ID]: connectionId,
        [CanonicalFields.WEBSOCKET_URL]: url,
        [CanonicalFields.WEBSOCKET_PROTOCOLS]: protocols?.join(','),
        [CanonicalFields.TIMESTAMP]: Date.now()
      } as CanonicalWebSocketContext;
    }

    return adapter.trace('canonical_websocket_connection_start', () => {
      const context = canonicalChatTracer.startWebSocketConnection(
        connectionId,
        url,
        protocols,
        traceId,
        parentSpanId
      );
      activeConnections.current.set(connectionId, context);
      return context;
    });
  }, [adapter]);

  const logMessage = useCallback((
    connectionId: string,
    direction: 'sent' | 'received',
    messageType: 'text' | 'binary' | 'ping' | 'pong' | 'close',
    size: number
  ) => {
    const context = activeConnections.current.get(connectionId);
    if (context) {
      canonicalChatTracer.logWebSocketMessage(context, direction, messageType, size);
    }
  }, []);

  const endConnection = useCallback((
    connectionId: string,
    closeCode?: number,
    closeReason?: string
  ) => {
    const context = activeConnections.current.get(connectionId);
    if (context) {
      canonicalChatTracer.endWebSocketConnection(context, closeCode, closeReason);
      activeConnections.current.delete(connectionId);
    }
  }, []);

  return {
    startConnection,
    logMessage,
    endConnection,
    activeConnections: activeConnections.current
  };
}

/**
 * React hook для канонической трассировки UI событий
 */
export function useCanonicalUITracing() {
  const adapter = useCanonicalBrowserAdapter();

  const traceUIEvent = useCallback((
    eventType: string,
    elementId?: string,
    data?: any
  ) => {
    if (!TraceConfigBrowser.isTracingEnabled()) {
      return {
        [CanonicalFields.TRACE_ID]: 'mock-trace-id',
        [CanonicalFields.SPAN_ID]: 'mock-span-id'
      } as CanonicalSpanContext;
    }

    return adapter.trace(`canonical_ui_${eventType.toLowerCase()}`, () => {
      const context = canonicalChatTracer.startSpan(`ui_${eventType.toLowerCase()}`);

      // Создаем событие UI с каноническими полями
      // Здесь можно добавить специфичные UI поля если они будут добавлены в схему

      canonicalChatTracer.endSpan(context, 0, 'complete');
      return context;
    });
  }, [adapter]);

  const traceClick = useCallback((elementId: string, data?: any) => {
    return traceUIEvent('CLICK', elementId, data);
  }, [traceUIEvent]);

  const traceInput = useCallback((elementId: string, value?: string) => {
    return traceUIEvent('INPUT', elementId, { valueLength: value?.length || 0 });
  }, [traceUIEvent]);

  return {
    traceUIEvent,
    traceClick,
    traceInput
  };
}

/**
 * Создает каноническую обертку для ClientApi с трассировкой
 */
export function createCanonicalTracedClientApi(api: ClientApi, adapter?: TraceAdapter): ClientApi {
  if (!TraceConfigBrowser.isTracingEnabled()) {
    return api;
  }

  const traceAdapter = adapter || new BrowserAdapter({
    telemetryEndpoint: '/api/telemetry',
    enableLocalStorage: true,
    maxStoredEvents: 1000
  });

  const tracedApi: ClientApi = { ...api };

  // Трассировка onApplyInEditor с каноническими полями
  if (api.onApplyInEditor) {
    const originalApply = api.onApplyInEditor;
    tracedApi.onApplyInEditor = async (content: string) => {
      return traceAdapter.traceAsync('canonical_apply_in_editor', async () => {
        const context = canonicalChatTracer.startSpan('apply_in_editor');
        const startTime = Date.now();

        try {
          const result = await originalApply(content);
          const duration = Date.now() - startTime;

          canonicalChatTracer.endSpan(context, duration, 'complete');
          return result;
        } catch (error) {
          const duration = Date.now() - startTime;
          canonicalChatTracer.endSpan(context, duration, 'error');
          throw error;
        }
      });
    };
  }

  // Трассировка onApplyInEditorV2 (Smart Apply) с каноническими полями
  if (api.onApplyInEditorV2) {
    const originalApplyV2 = api.onApplyInEditorV2;
    tracedApi.onApplyInEditorV2 = async (content: string, options?: any) => {
      return traceAdapter.traceAsync('canonical_smart_apply_in_editor', async () => {
        const context = canonicalChatTracer.startSpan('smart_apply_in_editor');
        const startTime = Date.now();

        try {
          const result = await originalApplyV2(content, options);
          const duration = Date.now() - startTime;

          canonicalChatTracer.endSpan(context, duration, 'complete');
          return result;
        } catch (error) {
          const duration = Date.now() - startTime;
          canonicalChatTracer.endSpan(context, duration, 'error');
          throw error;
        }
      });
    };
  }

  // Трассировка openInEditor с каноническими полями
  if (api.openInEditor) {
    const originalOpen = api.openInEditor;
    tracedApi.openInEditor = async (target) => {
      const context = canonicalChatTracer.startSpan('open_in_editor');
      const startTime = Date.now();

      try {
        const result = await originalOpen(target);
        const duration = Date.now() - startTime;
        canonicalChatTracer.endSpan(context, duration, 'complete');
        return result;
      } catch (error) {
        const duration = Date.now() - startTime;
        canonicalChatTracer.endSpan(context, duration, 'error');
        throw error;
      }
    };
  }

  return tracedApi;
}

/**
 * Создает каноническую обертку для ServerApi с трассировкой
 */
export function createCanonicalTracedServerApi(api: ServerApi, adapter?: TraceAdapter): ServerApi {
  if (!TraceConfigBrowser.isTracingEnabled()) {
    return api;
  }

  const traceAdapter = adapter || new BrowserAdapter({
    telemetryEndpoint: '/api/telemetry',
    enableLocalStorage: true,
    maxStoredEvents: 1000
  });

  const tracedApi: ServerApi = { ...api };

  // Трассировка executeCommand с каноническими полями
  const originalExecuteCommand = api.executeCommand;
  tracedApi.executeCommand = async (command: ChatCommand) => {
    return traceAdapter.traceAsync('canonical_execute_chat_command', async () => {
      const context = canonicalChatTracer.startSpan('execute_chat_command');
      const startTime = Date.now();

      try {
        const result = await originalExecuteCommand(command);
        const duration = Date.now() - startTime;

        canonicalChatTracer.endSpan(context, duration, 'complete');
        return result;
      } catch (error) {
        const duration = Date.now() - startTime;
        canonicalChatTracer.endSpan(context, duration, 'error');
        throw error;
      }
    });
  };

  // Трассировка navigate с каноническими полями
  if (api.navigate) {
    const originalNavigate = api.navigate;
    tracedApi.navigate = async (view) => {
      const context = canonicalChatTracer.startSpan('navigate_chat_view');
      const startTime = Date.now();

      try {
        const result = await originalNavigate(view);
        const duration = Date.now() - startTime;

        canonicalChatTracer.endSpan(context, duration, 'complete');
        return result;
      } catch (error) {
        const duration = Date.now() - startTime;
        canonicalChatTracer.endSpan(context, duration, 'error');
        throw error;
      }
    };
  }

  return tracedApi;
}