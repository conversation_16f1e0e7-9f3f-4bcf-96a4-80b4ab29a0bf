/**
 * Browser Adapter для tabby-chat-panel
 * 
 * Создан на основе backup/adapters/BrowserAdapter.ts с адаптацией под новую структуру filin-base.
 * Убраны проверки browser API (всегда доступны в браузере).
 * Полная интеграция с React без условных проверок.
 * 
 * Особенности:
 * - React Context для управления состоянием трассировки
 * - LocalStorage для персистентного хранения событий
 * - Fetch API для отправки телеметрии
 * - Автоматическое батчирование с настраиваемыми параметрами
 * - SSE трассировка с измерением time_to_first_token/time_to_last_byte
 */

import {
  TraceEvent,
  TraceEventTypes,
  TraceEventType,
  CanonicalFields
} from 'filin-base/browser';
import { W3CTraceContext } from 'filin-base/browser';
import type { SpanContext } from 'filin-base/browser';
import { BrowserTransportManager, BrowserTransportManagerConfig } from '../transports/BrowserTransportManager';
import { TraceConfigBrowser } from '../trace/TraceConfigBrowser';
import { TraceUtilsBrowser } from '../trace/TraceUtilsBrowser';

type StopReason = "complete" | "canceled_by_user" | "deadline_exceeded" | "error";

/**
 * Конфигурация браузерного адаптера
 */
export interface BrowserAdapterConfig {
  telemetryEndpoint?: string;
  enableLocalStorage?: boolean;
  maxStoredEvents?: number;
  batchSize?: number;
  batchTimeoutMs?: number;
  context?: SpanContext;
  transports?: BrowserTransportManagerConfig;
  jaegerEndpoint?: string;
  grafanaEndpoint?: string;
  grafanaApiKey?: string;
  developmentMode?: boolean;
}

/**
 * Интерфейс адаптера трассировки
 */
export interface TraceAdapter {
  getCurrentContext(): SpanContext | null;
  setCurrentContext(context: SpanContext | null): void;
  trace<T>(operation: string, fn: () => T, context?: SpanContext): T;
  traceAsync<T>(operation: string, fn: () => Promise<T>, context?: SpanContext): Promise<T>;
  logEvent(event: TraceEvent): void;
  exportTelemetry(): Promise<void>;
  createSpan(operation: string, context?: SpanContext | null): Span;
  injectContext(headers: Record<string, string>, context: SpanContext): void;
}

/**
 * Интерфейс спана
 */
export interface Span {
  readonly traceId: string;
  readonly spanId: string;
  readonly parentSpanId?: string;
  setAttributes(attributes: Record<string, any>): void;
  setStatus(status: { code: string; message?: string }): void;
  end(): void;
}

/**
 * React Context для трассировки
 */
export interface TraceContextValue {
  context: SpanContext | null;
  setContext: (context: SpanContext | null) => void;
  createSpan: (operation: string) => Span;
}

/**
 * React hook для доступа к контексту трассировки
 * Должен быть реализован в consuming приложении
 */
export function useTraceContext(): TraceContextValue {
  throw new Error('useTraceContext must be implemented by the consuming application with React.createContext');
}

/**
 * Простая реализация спана
 */
class BrowserSpan implements Span {
  public readonly traceId: string;
  public readonly spanId: string;
  public readonly parentSpanId?: string;

  private attributes: Record<string, any> = {};
  private status?: { code: string; message?: string };
  private adapter: BrowserAdapter;
  private operation: string;
  private startTime: number;

  constructor(
    traceId: string,
    spanId: string,
    operation: string,
    adapter: BrowserAdapter,
    parentSpanId?: string
  ) {
    this.traceId = traceId;
    this.spanId = spanId;
    this.parentSpanId = parentSpanId;
    this.operation = operation;
    this.adapter = adapter;
    this.startTime = Date.now();
  }

  setAttributes(attributes: Record<string, any>): void {
    Object.assign(this.attributes, attributes);
  }

  setStatus(status: { code: string; message?: string }): void {
    this.status = status;
  }

  end(): void {
    const duration = Date.now() - this.startTime;
    const stopReason: StopReason = this.status?.code === 'ERROR' ? 'error' : 'complete';

    const event = this.adapter.createSpanEndEvent(
      {
        traceId: this.traceId,
        spanId: this.spanId,
        parentSpanId: this.parentSpanId,
        traceparent: W3CTraceContext.createTraceparent(this.traceId, this.spanId)
      },
      this.operation,
      this.startTime,
      stopReason
    );

    // Добавляем атрибуты
    Object.assign(event, this.attributes);

    if (this.status?.message) {
      event[CanonicalFields.ERROR_MESSAGE] = this.status.message;
    }

    this.adapter.logEvent(event);
  }
}

/**
 * Browser Adapter implementation
 */
export class BrowserAdapter implements TraceAdapter {
  private currentContext: SpanContext | null = null;
  private config: BrowserAdapterConfig;
  private transportManager: BrowserTransportManager;
  private flushInterval?: number;

  constructor(config: BrowserAdapterConfig = {}) {
    this.config = {
      telemetryEndpoint: '/api/telemetry',
      enableLocalStorage: true,
      maxStoredEvents: 1000,
      batchSize: 50,
      batchTimeoutMs: 30000,
      ...config
    };

    this.transportManager = this.initializeTransportManager();
    this.setupPeriodicFlush();
    this.setupBeforeUnloadHandler();
  }

  getCurrentContext(): SpanContext | null {
    return this.currentContext;
  }

  setCurrentContext(context: SpanContext | null): void {
    this.currentContext = context;
  }

  trace<T>(operation: string, fn: () => T, context?: SpanContext): T {
    const previousContext = this.currentContext;
    const traceContext = context || this.createNewContext(operation);

    this.setCurrentContext(traceContext);
    const startTime = Date.now();

    // Log SPAN_START
    this.logEvent(this.createSpanStartEvent(traceContext, operation));

    try {
      const result = fn();
      this.logEvent(this.createSpanEndEvent(traceContext, operation, startTime, 'complete'));
      return result;
    } catch (error) {
      this.logEvent(this.createSpanEndEvent(traceContext, operation, startTime, 'error'));
      throw error;
    } finally {
      this.setCurrentContext(previousContext);
    }
  }

  async traceAsync<T>(operation: string, fn: () => Promise<T>, context?: SpanContext): Promise<T> {
    const previousContext = this.currentContext;
    const traceContext = context || this.createNewContext(operation);

    this.setCurrentContext(traceContext);
    const startTime = Date.now();

    // Log SPAN_START
    this.logEvent(this.createSpanStartEvent(traceContext, operation));

    try {
      const result = await fn();
      this.logEvent(this.createSpanEndEvent(traceContext, operation, startTime, 'complete'));
      return result;
    } catch (error) {
      this.logEvent(this.createSpanEndEvent(traceContext, operation, startTime, 'error'));
      throw error;
    } finally {
      this.setCurrentContext(previousContext);
    }
  }

  logEvent(event: TraceEvent): void {
    // Отправляем через transport manager
    this.transportManager.send(event).catch(error => {
      console.error('[BrowserAdapter] Failed to send event:', error);
    });
  }

  async exportTelemetry(): Promise<void> {
    // Принудительная отправка всех накопленных данных
    await this.transportManager.flush();
  }

  createSpan(operation: string, context?: SpanContext | null): Span {
    const traceContext = context || this.createNewContext(operation);
    return new BrowserSpan(
      traceContext.traceId,
      traceContext.spanId,
      operation,
      this,
      traceContext.parentSpanId
    );
  }

  injectContext(headers: Record<string, string>, context: SpanContext): void {
    headers['traceparent'] = context.traceparent;
    if (context.tracestate) {
      headers['tracestate'] = context.tracestate;
    }
    if (context.baggage) {
      headers['baggage'] = context.baggage;
    }
  }

  /**
   * Создает новый контекст трассировки
   */
  private createNewContext(operation: string): SpanContext {
    return W3CTraceContext.createSpanContext(operation, this.currentContext || undefined);
  }

  /**
   * Создает событие начала спана
   */
  createSpanStartEvent(context: SpanContext, operation: string): TraceEvent {
    return TraceUtilsBrowser.createBaseEvent(
      TraceEventTypes.SPAN_START,
      context.traceId,
      context.spanId,
      context.parentSpanId,
      TraceConfigBrowser.getServiceInfo(),
      'browser-adapter',
      'INFO'
    );
  }

  /**
   * Создает событие окончания спана
   */
  createSpanEndEvent(
    context: SpanContext,
    operation: string,
    startTime: number,
    stopReason: StopReason
  ): TraceEvent {
    const event = TraceUtilsBrowser.createBaseEvent(
      TraceEventTypes.SPAN_END,
      context.traceId,
      context.spanId,
      context.parentSpanId,
      TraceConfigBrowser.getServiceInfo(),
      'browser-adapter',
      'INFO'
    );

    event.operation = operation;
    event[CanonicalFields.DURATION_MS] = Date.now() - startTime;
    event[CanonicalFields.STOP_REASON] = stopReason;

    return event;
  }

  /**
   * Инициализирует transport manager
   */
  private initializeTransportManager(): BrowserTransportManager {
    let transportConfig: BrowserTransportManagerConfig;

    // Определяем конфигурацию на основе настроек
    if (this.config.developmentMode) {
      transportConfig = BrowserTransportManager.createDevelopmentConfig();
    } else if (this.config.jaegerEndpoint) {
      transportConfig = BrowserTransportManager.createJaegerConfig(this.config.jaegerEndpoint);
    } else if (this.config.grafanaEndpoint) {
      transportConfig = BrowserTransportManager.createGrafanaConfig(
        this.config.grafanaEndpoint,
        this.config.grafanaApiKey
      );
    } else if (this.config.transports) {
      transportConfig = this.config.transports;
    } else {
      // Конфигурация по умолчанию с LocalStorage и Console
      transportConfig = BrowserTransportManager.createDefaultConfig();

      // Включаем LocalStorage если разрешено
      if (this.config.enableLocalStorage && transportConfig.localStorage) {
        transportConfig.localStorage = {
          enabled: true,
          maxEvents: this.config.maxStoredEvents || 1000,
          storageKey: transportConfig.localStorage.storageKey || 'filin-trace-events'
        };
      }

      // Добавляем Fetch transport если указан endpoint
      if (this.config.telemetryEndpoint) {
        transportConfig.fetch = {
          enabled: true,
          endpoint: this.config.telemetryEndpoint,
          batchSize: this.config.batchSize || 50,
          batchTimeout: this.config.batchTimeoutMs || 30000
        };
      }

      // Проверяем конфигурацию OTLP из TraceConfig
      const traceConfig = TraceConfigBrowser.loadDefaultConfig();
      if (traceConfig.otlp?.endpoint) {
        transportConfig.otlp = {
          enabled: true,
          endpoint: traceConfig.otlp.endpoint,
          protocol: 'http',
          headers: traceConfig.otlp.headers || {},
          batchSize: this.config.batchSize || 50,
          batchTimeout: this.config.batchTimeoutMs || 10000
        };
      }
    }

    return new BrowserTransportManager(transportConfig);
  }

  /**
   * Добавляет транспорт
   */
  addTransport(name: string, transport: any): void {
    this.transportManager.addTransport(name, transport);
  }

  /**
   * Получает transport manager для расширенного управления
   */
  getTransportManager(): BrowserTransportManager {
    return this.transportManager;
  }

  /**
   * Настраивает периодическую отправку
   */
  private setupPeriodicFlush(): void {
    // Проверяем доступность window (для тестового окружения)
    if (typeof window === 'undefined') return;

    // Периодически отправляем накопленные данные
    const flushInterval = this.config.batchTimeoutMs || 30000;
    this.flushInterval = window.setInterval(async () => {
      try {
        await this.transportManager.flush();
      } catch (error) {
        console.warn('[BrowserAdapter] Periodic flush failed:', error);
      }
    }, flushInterval);
  }

  /**
   * Настраивает обработчик закрытия страницы
   */
  private setupBeforeUnloadHandler(): void {
    // Проверяем доступность window и document (для тестового окружения)
    if (typeof window === 'undefined' || typeof document === 'undefined') return;

    // Отправляем данные перед закрытием страницы
    window.addEventListener('beforeunload', async () => {
      try {
        await this.transportManager.flush();
      } catch (error) {
        console.warn('[BrowserAdapter] Before unload flush failed:', error);
      }
    });

    // Также обрабатываем скрытие страницы (для мобильных браузеров)
    document.addEventListener('visibilitychange', async () => {
      if (document.visibilityState === 'hidden') {
        try {
          await this.transportManager.flush();
        } catch (error) {
          console.warn('[BrowserAdapter] Visibility change flush failed:', error);
        }
      }
    });
  }

  /**
   * Создает traced fetch wrapper для HTTP запросов
   */
  createTracedFetch(): typeof fetch {
    const adapter = this;

    return async function tracedFetch(input: RequestInfo | URL, init?: RequestInit): Promise<Response> {
      const context = adapter.getCurrentContext();
      const span = adapter.createSpan('HTTP Request', context);

      try {
        // Инжекция контекста в заголовки
        const headers = new Headers(init?.headers);
        const traceContext = {
          traceId: span.traceId,
          spanId: span.spanId,
          parentSpanId: span.parentSpanId,
          traceparent: W3CTraceContext.createTraceparent(span.traceId, span.spanId)
        };

        const headersObj: Record<string, string> = {};
        headers.forEach((value, key) => {
          headersObj[key] = value;
        });

        adapter.injectContext(headersObj, traceContext);

        // Обновление заголовков
        Object.entries(headersObj).forEach(([key, value]) => {
          headers.set(key, value);
        });

        const url = typeof input === 'string' ? input : input.toString();
        const method = init?.method || 'GET';

        span.setAttributes({
          [CanonicalFields.HTTP_REQUEST_METHOD]: method,
          [CanonicalFields.URL_PATH]: url,
        });

        const response = await fetch(input, { ...init, headers });

        span.setAttributes({
          [CanonicalFields.HTTP_RESPONSE_STATUS_CODE]: response.status,
        });

        return response;
      } catch (error: any) {
        span.setStatus({ code: 'ERROR', message: error?.message });
        throw error;
      } finally {
        span.end();
      }
    };
  }

  /**
   * Создает SSE tracer для стриминговых операций
   */
  createSSETracer(url: string, init?: RequestInit) {
    const adapter = this;
    const context = adapter.getCurrentContext();
    const span = adapter.createSpan('HTTP SSE', context);
    const started = performance.now();

    return {
      async start(): Promise<EventSource> {
        try {
          // Инжекция контекста в URL параметры (SSE не поддерживает кастомные заголовки)
          const urlObj = new URL(url);
          const traceContext = {
            traceId: span.traceId,
            spanId: span.spanId,
            parentSpanId: span.parentSpanId,
            traceparent: W3CTraceContext.createTraceparent(span.traceId, span.spanId)
          };

          if (traceContext.traceparent) {
            urlObj.searchParams.set('traceparent', traceContext.traceparent);
          }

          const eventSource = new EventSource(urlObj.toString());
          let gotFirst = false;
          let chunks = 0;
          let bytes = 0;
          const N = 10; // Логируем каждый 10-й чанк

          // Логируем начало стрима
          const streamStartEvent = adapter.createBaseEvent(
            TraceEventTypes.CHAT_STREAM_START,
            span.traceId,
            span.spanId,
            span.parentSpanId
          );
          streamStartEvent[CanonicalFields.HTTP_REQUEST_METHOD] = 'GET';
          streamStartEvent[CanonicalFields.URL_PATH] = url;
          streamStartEvent[CanonicalFields.STREAM_ID] = `s-${Date.now()}`;
          adapter.logEvent(streamStartEvent);

          eventSource.onmessage = (event) => {
            chunks++;
            bytes += new Blob([event.data]).size;

            if (!gotFirst) {
              const ttfb = Math.round(performance.now() - started);
              const firstChunkEvent = adapter.createBaseEvent(
                TraceEventTypes.CHAT_STREAM_CHUNK,
                span.traceId,
                span.spanId,
                span.parentSpanId
              );
              firstChunkEvent[CanonicalFields.STREAM_SEQ] = 1;
              firstChunkEvent[CanonicalFields.TIME_TO_FIRST_TOKEN] = ttfb;
              adapter.logEvent(firstChunkEvent);
              gotFirst = true;
            } else if (chunks % N === 0) {
              const chunkEvent = adapter.createBaseEvent(
                TraceEventTypes.CHAT_STREAM_CHUNK,
                span.traceId,
                span.spanId,
                span.parentSpanId
              );
              chunkEvent[CanonicalFields.STREAM_SEQ] = chunks;
              adapter.logEvent(chunkEvent);
            }
          };

          eventSource.onerror = () => {
            const streamEndEvent = adapter.createBaseEvent(
              TraceEventTypes.CHAT_STREAM_END,
              span.traceId,
              span.spanId,
              span.parentSpanId
            );
            streamEndEvent[CanonicalFields.STOP_REASON] = 'error';
            adapter.logEvent(streamEndEvent);
            span.setStatus({ code: 'ERROR', message: 'SSE connection error' });
            span.end();
          };

          // Обработка закрытия
          const originalClose = eventSource.close.bind(eventSource);
          eventSource.close = () => {
            const ttlb = Math.round(performance.now() - started);
            const streamEndEvent = adapter.createBaseEvent(
              TraceEventTypes.CHAT_STREAM_END,
              span.traceId,
              span.spanId,
              span.parentSpanId
            );
            streamEndEvent[CanonicalFields.STREAM_CHUNKS_TOTAL] = chunks;
            streamEndEvent[CanonicalFields.BYTES_TOTAL] = bytes;
            streamEndEvent[CanonicalFields.TIME_TO_LAST_BYTE] = ttlb;
            streamEndEvent[CanonicalFields.STOP_REASON] = 'complete';
            adapter.logEvent(streamEndEvent);
            span.end();
            originalClose();
          };

          return eventSource;
        } catch (error: any) {
          const streamEndEvent = adapter.createBaseEvent(
            TraceEventTypes.CHAT_STREAM_END,
            span.traceId,
            span.spanId,
            span.parentSpanId
          );
          streamEndEvent[CanonicalFields.STOP_REASON] = 'error';
          streamEndEvent[CanonicalFields.ERROR_MESSAGE] = String(error);
          adapter.logEvent(streamEndEvent);
          span.setStatus({ code: 'ERROR', message: error?.message });
          span.end();
          throw error;
        }
      }
    };
  }

  /**
   * Создает базовое событие (helper метод)
   */
  private createBaseEvent(
    eventType: TraceEventType,
    traceId: string,
    spanId: string,
    parentSpanId?: string
  ): TraceEvent {
    return TraceUtilsBrowser.createBaseEvent(
      eventType,
      traceId,
      spanId,
      parentSpanId,
      TraceConfigBrowser.getServiceInfo(),
      'browser-adapter',
      'INFO'
    );
  }

  /**
   * Получает статистику всех транспортов
   */
  getTransportStats(): Record<string, any> {
    return this.transportManager.getStats();
  }

  /**
   * Получает список активных транспортов
   */
  getActiveTransports(): string[] {
    return this.transportManager.getActiveTransports();
  }

  /**
   * Создает конфигурацию для интеграции с Jaeger
   */
  static createJaegerConfig(jaegerEndpoint: string): BrowserAdapterConfig {
    return {
      jaegerEndpoint,
      enableLocalStorage: true,
      maxStoredEvents: 1000,
      batchSize: 50,
      batchTimeoutMs: 10000
    };
  }

  /**
   * Создает конфигурацию для интеграции с Grafana
   */
  static createGrafanaConfig(grafanaEndpoint: string, apiKey?: string): BrowserAdapterConfig {
    return {
      grafanaEndpoint,
      grafanaApiKey: apiKey,
      enableLocalStorage: true,
      maxStoredEvents: 2000,
      batchSize: 100,
      batchTimeoutMs: 5000
    };
  }

  /**
   * Создает конфигурацию для разработки
   */
  static createDevelopmentConfig(): BrowserAdapterConfig {
    return {
      developmentMode: true,
      enableLocalStorage: true,
      maxStoredEvents: 5000,
      batchSize: 10,
      batchTimeoutMs: 2000
    };
  }

  /**
   * Dispose adapter and clean up resources
   */
  async dispose(): Promise<void> {
    // Очищаем интервал
    if (this.flushInterval && typeof window !== 'undefined') {
      window.clearInterval(this.flushInterval);
      this.flushInterval = undefined;
    }

    // Отправляем последние данные и закрываем транспорты
    await this.transportManager.close();

    console.log('[BrowserAdapter] Disposed');
  }
}