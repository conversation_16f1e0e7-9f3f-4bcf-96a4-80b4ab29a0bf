/**
 * HTTP Integration utilities for W3C Trace Context
 * 
 * Provides utilities for integrating W3C Trace Context with HTTP clients
 */

import { SpanContext } from './TraceContext';
import { ContextPropagation } from './ContextPropagation';

// Type definitions for browser APIs (to avoid dependency on DOM types)
interface EventSourceInit {
  withCredentials?: boolean;
  [key: string]: any; // Allow additional properties like headers
}

interface EventSource {
  addEventListener(type: string, listener: () => void): void;
}

interface WebSocket {
  addEventListener(type: string, listener: () => void): void;
  send(data: string): void;
}

export interface HttpRequestConfig {
  method?: string;
  url: string;
  headers?: Record<string, string>;
  body?: any;
  timeout?: number;
  idempotencyKey?: string;
}

export interface HttpResponse {
  status: number;
  statusText: string;
  headers: Record<string, string>;
  data: any;
}

/**
 * HTTP client wrapper that automatically injects W3C Trace Context
 */
export class TracedHttpClient {
  constructor(
    private baseClient: (config: HttpRequestConfig) => Promise<HttpResponse>,
    private getCurrentContext: () => SpanContext | null
  ) {}
  
  /**
   * Makes an HTTP request with automatic trace context injection
   */
  async request(config: HttpRequestConfig): Promise<HttpResponse> {
    const context = this.getCurrentContext();
    
    if (context) {
      // Inject trace context into headers
      config.headers = config.headers || {};
      ContextPropagation.injectHttpHeaders(config.headers, context);
      
      // Add Idempotency-Key if provided
      if (config.idempotencyKey) {
        config.headers['Idempotency-Key'] = config.idempotencyKey;
      }
    }
    
    return this.baseClient(config);
  }
  
  /**
   * Convenience methods for common HTTP verbs
   */
  async get(url: string, config?: Partial<HttpRequestConfig>): Promise<HttpResponse> {
    return this.request({ ...config, method: 'GET', url });
  }
  
  async post(url: string, data?: any, config?: Partial<HttpRequestConfig>): Promise<HttpResponse> {
    return this.request({ ...config, method: 'POST', url, body: data });
  }
  
  async put(url: string, data?: any, config?: Partial<HttpRequestConfig>): Promise<HttpResponse> {
    return this.request({ ...config, method: 'PUT', url, body: data });
  }
  
  async delete(url: string, config?: Partial<HttpRequestConfig>): Promise<HttpResponse> {
    return this.request({ ...config, method: 'DELETE', url });
  }
}

/**
 * Server-Side Events (SSE) client with trace context support
 */
export class TracedSSEClient {
  constructor(
    private getCurrentContext: () => SpanContext | null
  ) {}
  
  /**
   * Creates an SSE connection with trace context injection
   */
  createEventSource(url: string, options?: EventSourceInit): EventSource {
    const context = this.getCurrentContext();
    
    if (context && options) {
      // Inject trace context into headers if supported
      const headers = (options as any).headers || {};
      ContextPropagation.injectHttpHeaders(headers, context);
      (options as any).headers = headers;
    }
    
    // Use global EventSource constructor
    const EventSourceConstructor = (globalThis as any).EventSource;
    return new EventSourceConstructor(url, options);
  }
  
  /**
   * Creates a fetch-based SSE stream with trace context
   */
  async createFetchStream(url: string, init?: RequestInit): Promise<any> {
    const context = this.getCurrentContext();
    
    if (context) {
      init = init || {};
      init.headers = init.headers || {};
      ContextPropagation.injectHttpHeaders(init.headers as Record<string, string>, context);
    }
    
    const response = await fetch(url, init);
    return response.body;
  }
}

/**
 * WebSocket client with trace context support
 */
export class TracedWebSocketClient {
  constructor(
    private getCurrentContext: () => SpanContext | null
  ) {}
  
  /**
   * Creates a WebSocket connection with trace context injection
   */
  createWebSocket(url: string, protocols?: string | string[]): WebSocket {
    const context = this.getCurrentContext();
    
    if (context) {
      // Inject trace context into URL parameters
      url = ContextPropagation.injectWebSocketURL(url, context);
    }
    
    const WebSocketConstructor = (globalThis as any).WebSocket;
    const ws = new WebSocketConstructor(url, protocols);
    
    // Send trace context as first message after connection opens
    if (context) {
      ws.addEventListener('open', () => {
        const traceMessage = ContextPropagation.createWebSocketTraceMessage(context);
        ws.send(traceMessage);
      });
    }
    
    return ws;
  }
}

/**
 * Middleware for extracting trace context from incoming HTTP requests
 */
export class HttpTraceMiddleware {
  /**
   * Extracts trace context from incoming request headers
   */
  static extractContext(headers: Record<string, string | string[]>): SpanContext | null {
    // Normalize headers to string values
    const normalizedHeaders: Record<string, string> = {};
    for (const [key, value] of Object.entries(headers)) {
      normalizedHeaders[key.toLowerCase()] = Array.isArray(value) ? value[0] : value;
    }
    
    return ContextPropagation.extractHttpHeaders(normalizedHeaders);
  }
  
  /**
   * Creates Express.js middleware for trace context extraction
   */
  static createExpressMiddleware() {
    return (req: any, res: any, next: any) => {
      const context = HttpTraceMiddleware.extractContext(req.headers);
      if (context) {
        req.traceContext = context;
      }
      next();
    };
  }
  
  /**
   * Creates Fastify middleware for trace context extraction
   */
  static createFastifyMiddleware() {
    return async (request: any, reply: any) => {
      const context = HttpTraceMiddleware.extractContext(request.headers);
      if (context) {
        request.traceContext = context;
      }
    };
  }
}

/**
 * Utility functions for HTTP trace integration
 */
export class HttpTraceUtils {
  /**
   * Generates an idempotency key for HTTP requests
   */
  static generateIdempotencyKey(area: string, operation: string, customIdGenerator?: () => string): string {
    // Format: <area>-<op>-<id>
    // Используем переданный генератор или простой fallback
    const id = customIdGenerator ? customIdGenerator() : HttpTraceUtils.generateSimpleId();
    return `${area}-${operation}-${id}`;
  }
  
  /**
   * Простой генератор ID для fallback случаев
   * Не зависит от конкретных реализаций клиентов
   */
  private static generateSimpleId(): string {
    // Простой timestamp + random для независимости
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2, 8);
    return `${timestamp}-${random}`;
  }
  
  /**
   * Extracts idempotency key from request headers
   */
  static extractIdempotencyKey(headers: Record<string, string>): string | undefined {
    return headers['idempotency-key'] || headers['Idempotency-Key'];
  }
  
  /**
   * Validates idempotency key format
   */
  static isValidIdempotencyKey(key: string): boolean {
    // Format: <area>-<op>-<uuidv7>
    const parts = key.split('-');
    if (parts.length < 3) return false;
    
    const uuid = parts.slice(2).join('-');
    return /^[0-9a-f]{8}-[0-9a-f]{4}-7[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(uuid);
  }
}