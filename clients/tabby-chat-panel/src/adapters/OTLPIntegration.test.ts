/**
 * Интеграционные тесты OTLP экспорта для tabby-chat-panel
 * 
 * Проверяет:
 * - Настройку OTLP транспорта через переменные окружения
 * - Отправку трейсов в Jaeger
 * - Обработку CORS ограничений
 * - Валидацию OTLP payload
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { BrowserAdapter } from './BrowserAdapter';
import { OTLPTransport } from '../transports/OTLPTransport';
import { TraceEvent, CanonicalFields } from 'filin-base/browser';

// Mock fetch для тестов
global.fetch = vi.fn();

describe('OTLP Integration - tabby-chat-panel', () => {
  let mockFetch: ReturnType<typeof vi.fn>;
  let originalEnv: NodeJS.ProcessEnv;

  beforeEach(() => {
    mockFetch = vi.mocked(fetch);
    mockFetch.mockClear();
    
    // Сохраняем оригинальные переменные окружения
    originalEnv = { ...process.env };
    
    // Настраиваем успешный ответ от OTLP коллектора
    mockFetch.mockResolvedValue(new Response('{"partialSuccess":{}}', {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    }));
  });

  afterEach(() => {
    // Восстанавливаем переменные окружения
    process.env = originalEnv;
    vi.clearAllMocks();
  });

  describe('Environment Variables Configuration', () => {
    it('should configure OTLP transport from OTEL_EXPORTER_OTLP_ENDPOINT', async () => {
      // Устанавливаем переменные окружения
      process.env.OTEL_EXPORTER_OTLP_ENDPOINT = 'http://localhost:4317';
      process.env.OTEL_SERVICE_NAME = 'test-chat-panel';

      const adapter = new BrowserAdapter({
        enableLocalStorage: false
      });

      // Создаем тестовое событие
      const event = createTestTraceEvent();
      
      // Отправляем событие
      adapter.logEvent(event);
      
      // Принудительно отправляем накопленные данные
      await adapter.exportTelemetry();
      
      // Проверяем что был сделан HTTP запрос к OTLP endpoint
      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:4317/v1/traces',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json'
          })
        })
      );

      await adapter.dispose();
    });

    it('should parse OTEL_EXPORTER_OTLP_HEADERS correctly', async () => {
      process.env.OTEL_EXPORTER_OTLP_ENDPOINT = 'http://localhost:4317';
      process.env.OTEL_EXPORTER_OTLP_HEADERS = 'api-key=secret123,tenant=test-tenant';

      const transport = new OTLPTransport({
        enabled: true,
        endpoint: 'http://localhost:4317',
        protocol: 'http',
        batchSize: 1, // Немедленная отправка
        batchTimeout: 10
      });

      const event = createTestTraceEvent();
      await transport.send(event);
      
      // Принудительно отправляем
      await transport.flush();

      // Проверяем что заголовки были добавлены
      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:4317/v1/traces',
        expect.objectContaining({
          headers: expect.objectContaining({
            'api-key': 'secret123',
            'tenant': 'test-tenant'
          })
        })
      );

      await transport.close();
    });

    it('should use OTEL_SERVICE_NAME in resource attributes', async () => {
      process.env.OTEL_SERVICE_NAME = 'custom-chat-service';

      const transport = new OTLPTransport({
        enabled: true,
        endpoint: 'http://localhost:4317',
        protocol: 'http'
      });

      const startEvent = createTestTraceEvent('SPAN_START');
      const endEvent = createTestTraceEvent('SPAN_END', startEvent[CanonicalFields.SPAN_ID]);
      
      await transport.sendBatch([startEvent, endEvent]);

      // Проверяем payload
      const call = mockFetch.mock.calls[0];
      const payload = JSON.parse(call[1]?.body as string);
      
      expect(payload.resourceSpans[0].resource.attributes).toContainEqual({
        key: 'service.name',
        value: { stringValue: 'custom-chat-service' }
      });

      await transport.close();
    });
  });

  describe('Jaeger Integration', () => {
    it('should send traces to Jaeger successfully', async () => {
      const jaegerEndpoint = 'http://localhost:14268';
      
      const adapter = new BrowserAdapter({
        jaegerEndpoint,
        enableLocalStorage: false
      });

      // Создаем полный трейс с несколькими спанами
      const traceId = '4bf92f3577b34da6a3ce929d0e0e4736';
      const rootSpanId = '00f067aa0ba902b7';
      const childSpanId = '00f067aa0ba902b8';

      const events = [
        createTestTraceEvent('SPAN_START', rootSpanId, traceId),
        createTestTraceEvent('SPAN_START', childSpanId, traceId, rootSpanId),
        createTestTraceEvent('SPAN_END', childSpanId, traceId, rootSpanId),
        createTestTraceEvent('SPAN_END', rootSpanId, traceId)
      ];

      // Отправляем события
      for (const event of events) {
        adapter.logEvent(event);
      }

      await adapter.exportTelemetry();

      // Проверяем что запрос был отправлен в Jaeger
      expect(mockFetch).toHaveBeenCalledWith(
        `${jaegerEndpoint}/v1/traces`,
        expect.objectContaining({
          method: 'POST'
        })
      );

      // Проверяем структуру OTLP payload
      const call = mockFetch.mock.calls[0];
      const payload = JSON.parse(call[1]?.body as string);
      
      expect(payload.resourceSpans).toHaveLength(1);
      expect(payload.resourceSpans[0].scopeSpans[0].spans).toHaveLength(2);

      await adapter.dispose();
    });

    it('should handle Jaeger connection errors gracefully', async () => {
      // Настраиваем ошибку сети
      mockFetch.mockRejectedValue(new Error('Network error'));

      const adapter = new BrowserAdapter({
        jaegerEndpoint: 'http://unreachable:14268',
        enableLocalStorage: false,
        batchTimeoutMs: 100 // Быстрая отправка для теста
      });

      const event = createTestTraceEvent();
      adapter.logEvent(event);

      // Экспорт должен завершиться без исключения (ошибки логируются, но не выбрасываются)
      await expect(adapter.exportTelemetry()).resolves.toBeUndefined();

      await adapter.dispose();
    }, 10000);
  });

  describe('CORS Handling', () => {
    it('should include proper CORS headers', async () => {
      const transport = new OTLPTransport({
        enabled: true,
        endpoint: 'http://localhost:4317',
        protocol: 'http',
        batchSize: 1,
        batchTimeout: 10,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS'
        }
      });

      const event = createTestTraceEvent();
      await transport.send(event);
      await transport.flush();

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:4317/v1/traces',
        expect.objectContaining({
          headers: expect.objectContaining({
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS'
          })
        })
      );

      await transport.close();
    });
  });

  describe('OTLP Payload Validation', () => {
    it('should generate valid OTLP payload structure', async () => {
      const transport = new OTLPTransport({
        enabled: true,
        endpoint: 'http://localhost:4317',
        protocol: 'http'
      });

      const startEvent = createTestTraceEvent('SPAN_START');
      const endEvent = createTestTraceEvent('SPAN_END', startEvent[CanonicalFields.SPAN_ID]);
      
      await transport.sendBatch([startEvent, endEvent]);

      const call = mockFetch.mock.calls[0];
      const payload = JSON.parse(call[1]?.body as string);

      // Валидируем структуру OTLP
      expect(payload).toHaveProperty('resourceSpans');
      expect(payload.resourceSpans).toHaveLength(1);
      
      const resourceSpan = payload.resourceSpans[0];
      expect(resourceSpan).toHaveProperty('resource');
      expect(resourceSpan).toHaveProperty('scopeSpans');
      
      const scopeSpan = resourceSpan.scopeSpans[0];
      expect(scopeSpan).toHaveProperty('scope');
      expect(scopeSpan).toHaveProperty('spans');
      expect(scopeSpan.spans).toHaveLength(1);
      
      const span = scopeSpan.spans[0];
      expect(span).toHaveProperty('traceId');
      expect(span).toHaveProperty('spanId');
      expect(span).toHaveProperty('name');
      expect(span).toHaveProperty('kind');
      expect(span).toHaveProperty('startTimeUnixNano');
      expect(span).toHaveProperty('attributes');

      await transport.close();
    });

    it('should convert trace and span IDs to base64', async () => {
      const transport = new OTLPTransport({
        enabled: true,
        endpoint: 'http://localhost:4317',
        protocol: 'http'
      });

      const traceId = '4bf92f3577b34da6a3ce929d0e0e4736';
      const spanId = '00f067aa0ba902b7';
      
      const startEvent = createTestTraceEvent('SPAN_START', spanId, traceId);
      const endEvent = createTestTraceEvent('SPAN_END', spanId, traceId);
      
      await transport.sendBatch([startEvent, endEvent]);

      const call = mockFetch.mock.calls[0];
      const payload = JSON.parse(call[1]?.body as string);
      const span = payload.resourceSpans[0].scopeSpans[0].spans[0];

      // Проверяем что ID конвертированы в base64
      expect(span.traceId).not.toBe(traceId);
      expect(span.spanId).not.toBe(spanId);
      expect(typeof span.traceId).toBe('string');
      expect(typeof span.spanId).toBe('string');

      await transport.close();
    });

    it('should include error status for failed spans', async () => {
      const transport = new OTLPTransport({
        enabled: true,
        endpoint: 'http://localhost:4317',
        protocol: 'http'
      });

      const startEvent = createTestTraceEvent('SPAN_START');
      const endEvent = {
        ...createTestTraceEvent('SPAN_END', startEvent[CanonicalFields.SPAN_ID]),
        [CanonicalFields.STOP_REASON]: 'error',
        [CanonicalFields.ERROR_MESSAGE]: 'Test error occurred'
      };
      
      await transport.sendBatch([startEvent, endEvent]);

      const call = mockFetch.mock.calls[0];
      const payload = JSON.parse(call[1]?.body as string);
      const span = payload.resourceSpans[0].scopeSpans[0].spans[0];

      expect(span.status).toEqual({
        code: 2, // ERROR
        message: 'Test error occurred'
      });

      await transport.close();
    });
  });

  describe('Performance and Batching', () => {
    it('should batch multiple events efficiently', async () => {
      const transport = new OTLPTransport({
        enabled: true,
        endpoint: 'http://localhost:4317',
        protocol: 'http',
        batchSize: 10, // Большой размер батча
        batchTimeout: 50
      });

      // Отправляем несколько событий
      const events = Array.from({ length: 3 }, (_, i) => 
        createTestTraceEvent('SPAN_START', `span${i}`)
      );

      for (const event of events) {
        await transport.send(event);
      }

      // Принудительно отправляем
      await transport.flush();

      // Проверяем что запросы были сделаны
      expect(mockFetch).toHaveBeenCalled();

      await transport.close();
    });

    it('should handle large payloads without memory issues', async () => {
      const transport = new OTLPTransport({
        enabled: true,
        endpoint: 'http://localhost:4317',
        protocol: 'http',
        batchSize: 100
      });

      // Создаем большой батч событий
      const events = Array.from({ length: 100 }, (_, i) => {
        const startEvent = createTestTraceEvent('SPAN_START', `span${i}`, `trace${i}`);
        const endEvent = createTestTraceEvent('SPAN_END', `span${i}`, `trace${i}`);
        return [startEvent, endEvent];
      }).flat();

      await transport.sendBatch(events);

      // Проверяем что запрос был сделан
      expect(mockFetch).toHaveBeenCalledTimes(1);
      
      // Проверяем размер payload
      const call = mockFetch.mock.calls[0];
      const payloadSize = (call[1]?.body as string).length;
      expect(payloadSize).toBeGreaterThan(1000); // Должен быть достаточно большой

      await transport.close();
    });
  });
});

/**
 * Создает тестовое событие трассировки
 */
function createTestTraceEvent(
  eventType: string = 'SPAN_START',
  spanId: string = '00f067aa0ba902b7',
  traceId: string = '4bf92f3577b34da6a3ce929d0e0e4736',
  parentSpanId?: string
): TraceEvent {
  const now = Date.now();
  const event: TraceEvent = {
    ts: new Date().toISOString(),
    [CanonicalFields.TIMESTAMP_UNIX_NANOS]: (now * 1000000).toString(),
    [CanonicalFields.EVENT_ID]: `01934d2e-${spanId.substring(0, 4)}-7890-abcd-123456789abc`,
    [CanonicalFields.TRACE_ID]: traceId,
    [CanonicalFields.SPAN_ID]: spanId,
    level: 'INFO',
    event: eventType,
    'event.version': '1.0',
    'schema.version': 'filin.log.v1',
    'service.name': 'tabby-chat-panel',
    'service.version': '1.0.0',
    'service.instance.id': 'browser-test',
    'service.namespace': 'filin.chat',
    'deployment.environment': 'test',
    component: 'chat-panel',
    operation: 'test-operation'
  };
  
  if (parentSpanId) {
    event[CanonicalFields.PARENT_SPAN_ID] = parentSpanId;
  }

  if (eventType === 'SPAN_END') {
    event[CanonicalFields.DURATION_MS] = 150;
    event[CanonicalFields.STOP_REASON] = 'complete';
  }
  
  return event;
}