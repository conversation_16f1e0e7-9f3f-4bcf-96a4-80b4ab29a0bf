/**
 * Filin Base - Основная точка входа
 * 
 * Экспортирует только базовые компоненты и утилиты для использования
 * в других модулях экосистемы Filin (tabby-agent, tabby-chat-panel, vscode, intellij)
 * 
 * Конкретные адаптеры и транспорты создаются в каждом клиенте отдельно.
 * 
 * @example
 * ```typescript
 * // Импорт базовых компонентов трассировки
 * import { TraceUtilsBase, DataMasker, TraceConfigManager } from 'filin-base';
 * 
 * // Импорт конкретных компонентов
 * import { TraceEventTypes, generateTraceId } from 'filin-base/trace';
 * 
 * // Импорт W3C Trace Context
 * import { W3CTraceContext, W3CTraceState } from 'filin-base/w3c';
 * 
 * // Импорт базовых интерфейсов транспортов
 * import { TraceTransport, TransportManager } from 'filin-base/transports';
 * ```
 * 
 * @packageDocumentation
 */

// Экспорт трассировки
export * from './trace';
export { TraceConfigManager } from './trace/TraceConfig';
export { IdGenerator, setIdGenerator, getIdGenerator, FallbackIdGenerator, initializeFallbackGenerator } from './trace/IdGenerator';

// Экспорт W3C Trace Context компонентов (избегаем дублирования TraceContext)
export { W3CTraceContext, W3CTraceState, W3CBaggage, ContextPropagation } from './w3c';
export type { SpanContext } from './w3c';

// Экспорт схемы и валидации
export * from './schema';
export { FILIN_SCHEMA_ID, FILIN_SCHEMA_VERSION, SCHEMA_CONFIG } from './schema/SchemaConstants';

// Экспорт базовых интерфейсов транспортов (избегаем дублирования TraceEvent)
export {
  TraceTransport,
  TransportManager,
  TransportConfig,
  OTLPConfig,
  HTTPConfig,
  FileConfig,
  ConsoleConfig,
  BatchConfig,
  SamplingConfig,
  OTLPTransportBase
} from './transports';
export type { TransportManagerConfig } from './transports';