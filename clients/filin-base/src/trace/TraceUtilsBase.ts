/**
 * Filin Tracer - Базовые утилиты трассировки (общие для всех компонентов)
 * 
 * Принцип: Единая реализация общей функциональности для всех компонентов
 * Формат trace_id: W3C Trace Context (32 hex символа)
 * Все события логируются в JSON Lines формате
 */

import { TraceEvent, LogLevel, TraceEventType, StopReason } from './TraceTypes';
import { W3CTraceContext, W3CTraceState, W3CBaggage, ContextPropagation } from '../w3c';
import { CanonicalFields } from './CanonicalFields';
import { getIdGenerator } from './IdGenerator';

/**
 * Базовые утилиты для работы с trace_id и событиями (общие для всех компонентов)
 */
export class TraceUtilsBase {

  /**
   * Генерирует уникальный trace_id в формате W3C Trace Context (32 hex символа)
   * @return строка из 32 hex символов
   */
  static generateTraceId(): string {
    return W3CTraceContext.generateTraceId();
  }

  /**
   * Генерирует уникальный span_id в формате W3C Trace Context (16 hex символов)
   * @return строка из 16 hex символов
   */
  static generateSpanId(): string {
    return W3CTraceContext.generateSpanId();
  }

  /**
   * Генерирует UUIDv7 для event_id (time-ordered)
   * @return UUIDv7 строка
   */
  static generateEventId(): string {
    return getIdGenerator().generateEventId();
  }

  /**
   * Проверяет валидность trace_id (W3C Trace Context format)
   * @param traceId строка для проверки
   * @return true если trace_id валиден
   */
  static isValidTraceId(traceId: string): boolean {
    return W3CTraceContext.isValidTraceId(traceId);
  }

  /**
   * Проверяет валидность span_id (W3C Trace Context format)
   * @param spanId строка для проверки
   * @return true если span_id валиден
   */
  static isValidSpanId(spanId: string): boolean {
    return W3CTraceContext.isValidSpanId(spanId);
  }

  /**
   * Проверяет валидность event_id (UUIDv7 format)
   * @param eventId строка для проверки
   * @return true если event_id валиден
   */
  static isValidEventId(eventId: string): boolean {
    // UUIDv7 format: xxxxxxxx-xxxx-7xxx-xxxx-xxxxxxxxxxxx
    return /^[0-9a-f]{8}-[0-9a-f]{4}-7[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(eventId);
  }

  /**
   * Создает базовое событие трассировки в формате JSON Lines
   * @param event тип события
   * @param traceId идентификатор трассировки
   * @param spanId идентификатор спана
   * @param parentSpanId идентификатор родительского спана (опционально)
   * @param serviceInfo информация о сервисе
   * @param component компонент
   * @param level уровень логирования
   * @return базовое событие TraceEvent
   */
  static createBaseEvent(
    event: TraceEventType,
    traceId: string,
    spanId: string,
    parentSpanId: string | undefined,
    serviceInfo: {
      name: string;
      version: string;
      instanceId: string;
      namespace: string;
      environment: string;
    },
    component: string,
    level: LogLevel = "INFO"
  ): TraceEvent {
    const now = new Date();
    const tsUnixNanos = (BigInt(now.getTime()) * BigInt(1000000)).toString();

    const baseEvent: TraceEvent = {
      [CanonicalFields.TIMESTAMP]: now.toISOString(),
      [CanonicalFields.TIMESTAMP_UNIX_NANOS]: tsUnixNanos,
      [CanonicalFields.EVENT_ID]: TraceUtilsBase.generateEventId(),
      [CanonicalFields.TRACE_ID]: traceId,
      [CanonicalFields.SPAN_ID]: spanId,
      [CanonicalFields.LEVEL]: level,
      [CanonicalFields.EVENT]: event,
      [CanonicalFields.EVENT_VERSION]: "1.0",
      [CanonicalFields.SCHEMA_VERSION]: "filin.log.v1",
      [CanonicalFields.SERVICE_NAME]: serviceInfo.name,
      [CanonicalFields.SERVICE_VERSION]: serviceInfo.version,
      [CanonicalFields.SERVICE_INSTANCE_ID]: serviceInfo.instanceId,
      [CanonicalFields.SERVICE_NAMESPACE]: serviceInfo.namespace,
      [CanonicalFields.DEPLOYMENT_ENVIRONMENT]: serviceInfo.environment,
      [CanonicalFields.COMPONENT]: component
    };

    if (parentSpanId) {
      baseEvent[CanonicalFields.PARENT_SPAN_ID] = parentSpanId;
    }

    return baseEvent;
  }

  /**
   * Форматирует событие трассировки в JSON Lines формат
   * @param event событие трассировки
   * @return JSON строка для логирования
   */
  static formatTraceEventJsonLines(event: TraceEvent): string {
    return JSON.stringify(event);
  }

  /**
   * Вычисляет duration_ms от монотонных часов
   * @param startTime время начала (performance.now())
   * @return длительность в миллисекундах
   */
  static calculateDurationMs(startTime: number): number {
    return Math.round(performance.now() - startTime);
  }

  /**
   * Извлекает W3C Trace Context из LSP параметров
   * @param params параметры LSP запроса
   * @return объект с traceparent и baggage или null если не найден
   */
  static extractW3CContextFromLspParams(params: any): { traceparent?: string; baggage?: string } | null {
    const context = ContextPropagation.extractLSPParams(params);
    if (context) {
      return {
        traceparent: context.traceparent,
        baggage: context.baggage
      };
    }
    return null;
  }

  /**
   * Добавляет W3C Trace Context в LSP параметры
   * @param params существующие параметры (может быть null или undefined)
   * @param traceparent W3C traceparent заголовок
   * @param baggage W3C baggage заголовок (опционально)
   * @return обновленные параметры с x-filin-trace
   */
  static addW3CContextToLspParams(
    params: Record<string, any> | null | undefined,
    traceparent: string,
    baggage?: string
  ): Record<string, any> {
    const parsed = W3CTraceContext.parseTraceparent(traceparent);
    if (!parsed) {
      return params || {};
    }

    const context = {
      traceId: parsed.traceId,
      spanId: parsed.spanId,
      traceparent,
      baggage
    };

    return ContextPropagation.injectLSPParams(params, context);
  }

  /**
   * Парсит W3C traceparent заголовок
   * @param traceparent строка формата "00-<trace_id>-<span_id>-<flags>"
   * @return объект с trace_id, span_id, flags или null если невалиден
   */
  static parseTraceparent(traceparent: string): { traceId: string; spanId: string; flags: string } | null {
    const parsed = W3CTraceContext.parseTraceparent(traceparent);
    if (parsed) {
      return {
        traceId: parsed.traceId,
        spanId: parsed.spanId,
        flags: parsed.flags
      };
    }
    return null;
  }

  /**
   * Создает W3C traceparent заголовок
   * @param traceId идентификатор трассировки (32 hex символа)
   * @param spanId идентификатор спана (16 hex символов)
   * @param flags флаги (по умолчанию "01" для sampled)
   * @return строка формата "00-<trace_id>-<span_id>-<flags>"
   */
  static createTraceparent(traceId: string, spanId: string, flags: string = "01"): string {
    const sampled = flags === "01";
    return W3CTraceContext.createTraceparent(traceId, spanId, sampled);
  }

  /**
   * Форматирует событие детектора зависаний
   * @param eventType тип события (HANG_DETECTED, HTTP_TIMEOUT, LSP_TIMEOUT)
   * @param operation название операции или метод
   * @param traceId идентификатор трассировки
   * @param duration длительность операции в миллисекундах
   * @param stackTrace стек вызовов (опционально)
   * @return отформатированное сообщение для логирования
   */
  static formatHangEvent(
    eventType: string,
    operation: string,
    traceId: string,
    duration: number,
    stackTrace?: string
  ): string {
    const timestamp = new Date().toISOString();
    const stackPart = stackTrace ? ` stack=${stackTrace}` : '';
    return `[FilinTracer] ${eventType} ${operation} trace_id=${traceId} duration=${duration}ms timestamp=${timestamp}${stackPart}`;
  }

  /**
   * Получает стек вызовов для детектора зависаний
   * @return строка со стеком вызовов или undefined если недоступно
   */
  static getStackTrace(): string | undefined {
    try {
      const stack = new Error().stack;
      if (stack) {
        // Убираем первые 2 строки (Error и getStackTrace)
        const lines = stack.split('\n').slice(2);
        // Берем только первые 5 строк для компактности
        return lines.slice(0, 5).join('\\n');
      }
    } catch (e) {
      // Игнорируем ошибки получения стека
    }
    return undefined;
  }

  /**
   * Создает Filin-специфичный контекст трассировки с сессионными данными
   * @param operation название операции
   * @param ide название IDE (intellij, vscode, etc.)
   * @param sessionId идентификатор сессии
   * @param workspaceId идентификатор workspace
   * @param parent родительский контекст (опционально)
   * @return полный контекст трассировки
   */
  static createFilinContext(
    operation: string,
    ide: string,
    sessionId: string,
    workspaceId: string,
    parent?: { traceId: string; spanId: string; traceparent: string; tracestate?: string; baggage?: string }
  ) {
    return ContextPropagation.createFilinContext(operation, ide, sessionId, workspaceId, parent);
  }

  /**
   * Создает трассировочные заголовки для HTTP запросов
   * @param context контекст трассировки
   * @param idempotencyKey ключ идемпотентности (опционально)
   * @return заголовки для HTTP запроса
   */
  static createHttpHeaders(
    context: { traceId: string; spanId: string; traceparent: string; tracestate?: string; baggage?: string },
    idempotencyKey?: string
  ): Record<string, string> {
    const headers: Record<string, string> = {};
    ContextPropagation.injectHttpHeaders(headers, context);

    if (idempotencyKey) {
      headers['Idempotency-Key'] = idempotencyKey;
    }

    return headers;
  }

  /**
   * Извлекает контекст трассировки из HTTP заголовков
   * @param headers HTTP заголовки
   * @return контекст трассировки или null если не найден
   */
  static extractHttpContext(headers: Record<string, string | string[]>) {
    return ContextPropagation.extractHttpHeaders(headers);
  }

  /**
   * Создает Filin baggage с сессионными данными
   * @param ide название IDE
   * @param workspaceId идентификатор workspace
   * @param sessionId идентификатор сессии
   * @return baggage строка
   */
  static createFilinBaggage(ide: string, workspaceId: string, sessionId: string): string {
    return W3CBaggage.createFilinBaggage(ide, workspaceId, sessionId);
  }

  /**
   * Создает Filin tracestate с сессионными данными
   * @param sessionId идентификатор сессии
   * @param workspaceId идентификатор workspace
   * @param ide название IDE
   * @return tracestate строка
   */
  static createFilinTracestate(sessionId: string, workspaceId: string, ide: string): string {
    return W3CTraceState.createFilinTracestate(sessionId, workspaceId, ide);
  }

  /**
   * Валидирует baggage содержимое согласно правилам безопасности
   * @param baggage baggage строка
   * @return true если валиден, иначе выбрасывает исключение
   */
  static validateBaggageContent(baggage: string): boolean {
    return W3CBaggage.validateBaggageContent(baggage);
  }
}

/**
 * Экспорт функций для удобства использования
 */
export const generateTraceId = TraceUtilsBase.generateTraceId;
export const generateSpanId = TraceUtilsBase.generateSpanId;
export const generateEventId = TraceUtilsBase.generateEventId;