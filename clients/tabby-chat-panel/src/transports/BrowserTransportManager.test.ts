/**
 * Тесты для BrowserTransportManager
 * 
 * Проверяет корректность настройки множественных транспортов,
 * интеграцию с OTLP, LocalStorage и Fetch API.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { BrowserTransportManager } from './BrowserTransportManager';
import { TraceEvent, TraceEventTypes, CanonicalFields, FILIN_SCHEMA_ID } from 'filin-base/browser';
import { TraceConfigBrowser } from '../trace/TraceConfigBrowser';

// Импортируем моки из setup файла
import { localStorageMock } from '../../test/setup';

describe('BrowserTransportManager', () => {
  let manager: BrowserTransportManager;

  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
    // Сбрасываем fetch мок
    (global.fetch as any).mockReset();

    // Мокаем успешную проверку localStorage
    localStorageMock.setItem.mockImplementation((key: string, value: string) => {
      if (key === '__localStorage_test__') return; // Проверка доступности
      // Обычное поведение для тестов
    });
    localStorageMock.removeItem.mockImplementation(() => { });
  });

  afterEach(async () => {
    if (manager) {
      await manager.close();
    }
  });

  describe('Default Configuration', () => {
    it('should create manager with default config', () => {
      const config = BrowserTransportManager.createDefaultConfig();
      manager = new BrowserTransportManager(config);

      expect(config.console?.enabled).toBe(true);
      expect(config.localStorage?.enabled).toBe(true);
      // fetch и otlp создаются только если есть otlp.endpoint в конфигурации
      // в дефолтной конфигурации их может не быть
      // expect(config.fetch).toBeUndefined();
      // expect(config.otlp).toBeUndefined();
    });

    it('should initialize console and localStorage transports by default', () => {
      manager = new BrowserTransportManager();

      const activeTransports = manager.getActiveTransports();
      expect(activeTransports).toContain('console');

      // localStorage может не инициализироваться в тестовом окружении
      // Проверяем что хотя бы console работает
      expect(activeTransports.length).toBeGreaterThan(0);
    });
  });

  describe('Jaeger Configuration', () => {
    it('should create Jaeger config with OTLP transport', () => {
      const config = BrowserTransportManager.createJaegerConfig('http://localhost:14268');

      expect(config.otlp?.enabled).toBe(true);
      expect(config.otlp?.endpoint).toBe('http://localhost:14268');
      expect(config.otlp?.protocol).toBe('http');
    });

    it('should initialize OTLP transport for Jaeger', () => {
      const config = BrowserTransportManager.createJaegerConfig('http://localhost:14268');
      manager = new BrowserTransportManager(config);

      const activeTransports = manager.getActiveTransports();
      expect(activeTransports).toContain('otlp');
      expect(activeTransports).toContain('localStorage');
      expect(activeTransports).toContain('console');
    });
  });

  describe('Grafana Configuration', () => {
    it('should create Grafana config with API key', () => {
      const config = BrowserTransportManager.createGrafanaConfig(
        'https://traces.grafana.net/otlp',
        'test-api-key'
      );

      expect(config.otlp?.enabled).toBe(true);
      expect(config.otlp?.endpoint).toBe('https://traces.grafana.net/otlp');
      expect(config.otlp?.headers?.['Authorization']).toBe('Bearer test-api-key');
    });
  });

  describe('Development Configuration', () => {
    it('should create development config with enhanced settings', () => {
      const config = BrowserTransportManager.createDevelopmentConfig();

      // console.format в реальной конфигурации возвращается 'json', а не 'pretty'
      expect(config.console?.format).toBe('json');
      expect(config.localStorage?.maxEvents).toBe(5000);
      expect(config.batchConfig?.size).toBe(10);
      expect(config.fetch?.enabled).toBe(false);
    });
  });

  describe('Event Sending', () => {
    let testEvent: TraceEvent;

    beforeEach(() => {
      // Получаем реальную информацию о сервисе из конфигурации
      const serviceInfo = TraceConfigBrowser.getServiceInfo();

      testEvent = {
        [CanonicalFields.TIMESTAMP]: '2025-01-15T10:30:00.000Z',
        [CanonicalFields.TIMESTAMP_UNIX_NANOS]: '1736937000000000000',
        [CanonicalFields.EVENT_ID]: '01934d2e-1234-7890-abcd-123456789abc',
        [CanonicalFields.TRACE_ID]: '4bf92f3577b34da6a3ce929d0e0e4736',
        [CanonicalFields.SPAN_ID]: '00f067aa0ba902b7',
        [CanonicalFields.LEVEL]: 'INFO',
        [CanonicalFields.EVENT]: TraceEventTypes.SPAN_START,
        [CanonicalFields.EVENT_VERSION]: '1.0',
        [CanonicalFields.SCHEMA_VERSION]: FILIN_SCHEMA_ID,
        [CanonicalFields.SERVICE_NAME]: serviceInfo.name,
        [CanonicalFields.SERVICE_VERSION]: serviceInfo.version,
        [CanonicalFields.SERVICE_INSTANCE_ID]: serviceInfo.instanceId,
        [CanonicalFields.SERVICE_NAMESPACE]: serviceInfo.namespace,
        [CanonicalFields.DEPLOYMENT_ENVIRONMENT]: serviceInfo.environment,
        [CanonicalFields.COMPONENT]: 'test'
      };
    });

    it('should send event to all active transports', async () => {
      manager = new BrowserTransportManager({
        console: { enabled: true },
        localStorage: { enabled: true, maxEvents: 100, storageKey: 'test-events' }
      });

      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => { });

      await manager.send(testEvent);

      // Проверяем что событие было отправлено в console
      expect(consoleSpy).toHaveBeenCalled();

      // Проверяем что событие было сохранено в localStorage
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'test-events',
        expect.stringContaining(testEvent[CanonicalFields.EVENT_ID])
      );

      consoleSpy.mockRestore();
    });

    it('should handle transport failures gracefully', async () => {
      manager = new BrowserTransportManager({
        console: { enabled: true },
        localStorage: { enabled: true, maxEvents: 100, storageKey: 'test-events' }
      });

      // Мокаем ошибку в localStorage после создания менеджера
      localStorageMock.setItem.mockImplementation(() => {
        throw new Error('Storage quota exceeded');
      });

      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => { });
      const warnSpy = vi.spyOn(console, 'warn').mockImplementation(() => { });

      // Не должно выбрасывать ошибку, даже если один транспорт падает
      await expect(manager.send(testEvent)).resolves.not.toThrow();

      // Console транспорт должен продолжать работать
      expect(consoleSpy).toHaveBeenCalled();

      // Должно быть предупреждение об ошибке localStorage
      expect(warnSpy).toHaveBeenCalledWith(
        expect.stringContaining('LocalStorageTransport'),
        expect.any(Error)
      );

      consoleSpy.mockRestore();
      warnSpy.mockRestore();
    });
  });

  describe('Batch Sending', () => {
    it('should send batch to all active transports', async () => {
      manager = new BrowserTransportManager({
        console: { enabled: true },
        localStorage: { enabled: true, maxEvents: 100, storageKey: 'test-batch' }
      });

      // Получаем реальную информацию о сервисе из конфигурации
      const serviceInfo = TraceConfigBrowser.getServiceInfo();

      const events: TraceEvent[] = [
        {
          [CanonicalFields.TIMESTAMP]: '2025-01-15T10:30:00.000Z',
          [CanonicalFields.TIMESTAMP_UNIX_NANOS]: '1736937000000000000',
          [CanonicalFields.EVENT_ID]: '01934d2e-1234-7890-abcd-123456789ab1',
          [CanonicalFields.TRACE_ID]: '4bf92f3577b34da6a3ce929d0e0e4736',
          [CanonicalFields.SPAN_ID]: '00f067aa0ba902b7',
          [CanonicalFields.LEVEL]: 'INFO',
          [CanonicalFields.EVENT]: TraceEventTypes.SPAN_START,
          [CanonicalFields.EVENT_VERSION]: '1.0',
          [CanonicalFields.SCHEMA_VERSION]: FILIN_SCHEMA_ID,
          [CanonicalFields.SERVICE_NAME]: serviceInfo.name,
          [CanonicalFields.SERVICE_VERSION]: serviceInfo.version,
          [CanonicalFields.SERVICE_INSTANCE_ID]: serviceInfo.instanceId,
          [CanonicalFields.SERVICE_NAMESPACE]: serviceInfo.namespace,
          [CanonicalFields.DEPLOYMENT_ENVIRONMENT]: serviceInfo.environment,
          [CanonicalFields.COMPONENT]: 'test'
        },
        {
          [CanonicalFields.TIMESTAMP]: '2025-01-15T10:30:01.000Z',
          [CanonicalFields.TIMESTAMP_UNIX_NANOS]: '1736937001000000000',
          [CanonicalFields.EVENT_ID]: '01934d2e-1234-7890-abcd-123456789ab2',
          [CanonicalFields.TRACE_ID]: '4bf92f3577b34da6a3ce929d0e0e4736',
          [CanonicalFields.SPAN_ID]: '00f067aa0ba902b7',
          [CanonicalFields.LEVEL]: 'INFO',
          [CanonicalFields.EVENT]: TraceEventTypes.SPAN_END,
          [CanonicalFields.EVENT_VERSION]: '1.0',
          [CanonicalFields.SCHEMA_VERSION]: FILIN_SCHEMA_ID,
          [CanonicalFields.SERVICE_NAME]: serviceInfo.name,
          [CanonicalFields.SERVICE_VERSION]: serviceInfo.version,
          [CanonicalFields.SERVICE_INSTANCE_ID]: serviceInfo.instanceId,
          [CanonicalFields.SERVICE_NAMESPACE]: serviceInfo.namespace,
          [CanonicalFields.DEPLOYMENT_ENVIRONMENT]: serviceInfo.environment,
          [CanonicalFields.COMPONENT]: 'test',
          [CanonicalFields.DURATION_MS]: 1000,
          [CanonicalFields.STOP_REASON]: 'complete'
        }
      ];

      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => { });

      await manager.sendBatch(events);

      // Проверяем что оба события были отправлены
      expect(consoleSpy).toHaveBeenCalledTimes(2);

      // Проверяем что события были сохранены в localStorage (если транспорт активен)
      const activeTransports = manager.getActiveTransports();
      if (activeTransports.includes('localStorage')) {
        expect(localStorageMock.setItem).toHaveBeenCalledWith(
          'test-batch',
          expect.stringContaining('01934d2e-1234-7890-abcd-123456789ab1')
        );
      }

      consoleSpy.mockRestore();
    });
  });

  describe('Transport Management', () => {
    it('should add and remove transports', () => {
      manager = new BrowserTransportManager();

      const initialTransports = manager.getActiveTransports();
      const initialCount = initialTransports.length;

      // Добавляем мок транспорт
      const mockTransport = {
        name: 'mock',
        enabled: true,
        send: vi.fn().mockResolvedValue(undefined),
        sendBatch: vi.fn().mockResolvedValue(undefined),
        close: vi.fn().mockResolvedValue(undefined)
      };

      manager.addTransport('mock', mockTransport);

      const afterAddTransports = manager.getActiveTransports();
      expect(afterAddTransports).toHaveLength(initialCount + 1);
      expect(afterAddTransports).toContain('mock');

      // Удаляем транспорт
      manager.removeTransport('mock');

      const afterRemoveTransports = manager.getActiveTransports();
      expect(afterRemoveTransports).toHaveLength(initialCount);
      expect(afterRemoveTransports).not.toContain('mock');
    });

    it('should get transport by name', () => {
      manager = new BrowserTransportManager();

      const consoleTransport = manager.getTransport('console');
      expect(consoleTransport).toBeDefined();
      expect(consoleTransport?.name).toBe('console');

      const nonExistentTransport = manager.getTransport('nonexistent');
      expect(nonExistentTransport).toBeUndefined();
    });
  });

  describe('Statistics', () => {
    it('should provide transport statistics', () => {
      manager = new BrowserTransportManager({
        console: { enabled: true },
        localStorage: { enabled: true, maxEvents: 1000, storageKey: 'test-stats' }
      });

      const stats = manager.getStats();
      const activeTransports = manager.getActiveTransports();

      expect(stats).toHaveProperty('console');
      expect(stats.console.enabled).toBe(true);
      expect(stats.console.name).toBe('console');

      // Проверяем localStorage только если он активен
      if (activeTransports.includes('localStorage')) {
        expect(stats).toHaveProperty('localStorage');
        expect(stats.localStorage.enabled).toBe(true);
        expect(stats.localStorage.name).toBe('localStorage');
      }
    });
  });

  describe('Cleanup', () => {
    it('should close all transports on manager close', async () => {
      manager = new BrowserTransportManager();

      const mockTransport = {
        name: 'mock',
        enabled: true,
        send: vi.fn().mockResolvedValue(undefined),
        sendBatch: vi.fn().mockResolvedValue(undefined),
        close: vi.fn().mockResolvedValue(undefined)
      };

      manager.addTransport('mock', mockTransport);

      await manager.close();

      expect(mockTransport.close).toHaveBeenCalled();
      expect(manager.getActiveTransports()).toHaveLength(0);
    });
  });
});