/**
 * Filin Tracer - Общие типы и константы (общие для всех компонентов)
 * 
 * Принцип: Единые типы и константы для всех компонентов системы трассировки
 */

import { CanonicalFields } from './CanonicalFields';

/**
 * Canonical trace event fields and types
 * Based on OpenTelemetry semantic conventions and W3C Trace Context
 */

import { FILIN_SCHEMA_ID } from '../schema/SchemaConstants';

// Base event types
export const TraceEventTypes = {
  // Spans
  SPAN_START: 'SPAN_START',
  SPAN_END: 'SPAN_END',

  // LSP
  LSP_REQUEST: 'LSP_REQUEST',
  LSP_RESPONSE: 'LSP_RESPONSE',
  LSP_ERROR: 'LSP_ERROR',
  LSP_TIMEOUT: 'LSP_TIMEOUT',
  LSP_NOTIFICATION: 'LSP_NOTIFICATION',

  // HTTP
  HTTP_REQUEST: 'HTTP_REQUEST',
  HTTP_RESPONSE: 'HTTP_RESPONSE',
  HTTP_ERROR: 'HTTP_ERROR',
  HTTP_TIMEOUT: 'HTTP_TIMEOUT',

  // Server
  SERVER_REQUEST: 'SERVER_REQUEST',
  SERVER_RESPONSE: 'SERVER_RESPONSE',
  SERVER_SLOW_REQUEST: 'SERVER_SLOW_REQUEST',

  // Chat
  CHAT_MESSAGE_SENT: 'CHAT_MESSAGE_SENT',
  CHAT_REQUEST: 'CHAT_REQUEST',
  CHAT_RESPONSE: 'CHAT_RESPONSE',
  CHAT_STREAM_START: 'CHAT_STREAM_START',
  CHAT_STREAM_CHUNK: 'CHAT_STREAM_CHUNK',
  CHAT_STREAM_END: 'CHAT_STREAM_END',

  // Diagnostics
  SLOW_OPERATION: 'SLOW_OPERATION',
  HANG_DETECTED: 'HANG_DETECTED'
} as const;

export type TraceEventType = typeof TraceEventTypes[keyof typeof TraceEventTypes];

/**
 * Stop reasons for operations
 */
export type StopReason = "complete" | "canceled_by_user" | "deadline_exceeded" | "error";

/**
 * Log levels
 */
export type LogLevel = "DEBUG" | "INFO" | "WARN" | "ERROR";

/**
 * Canonical event list (matches JSON Schema)
 */
export const CANONICAL_EVENTS: TraceEventType[] = [
  "SPAN_START", "SPAN_END",
  "LSP_REQUEST", "LSP_RESPONSE", "LSP_ERROR", "LSP_TIMEOUT", "LSP_NOTIFICATION",
  "HTTP_REQUEST", "HTTP_RESPONSE", "HTTP_ERROR", "HTTP_TIMEOUT",
  "SERVER_REQUEST", "SERVER_RESPONSE", "SERVER_SLOW_REQUEST",
  "CHAT_MESSAGE_SENT", "CHAT_REQUEST", "CHAT_RESPONSE",
  "CHAT_STREAM_START", "CHAT_STREAM_CHUNK", "CHAT_STREAM_END",
  "SLOW_OPERATION", "HANG_DETECTED"
];

/**
 * Events that can have stop_reason
 */
export const STOP_REASON_EVENTS: TraceEventType[] = [
  "SPAN_END", "CHAT_STREAM_END", "HTTP_ERROR", "LSP_ERROR", "HTTP_TIMEOUT", "LSP_TIMEOUT"
];

/**
 * HTTP events that cannot have duration_ms (duration is in SPAN_END)
 */
export const HTTP_EVENTS_NO_DURATION: TraceEventType[] = [
  "HTTP_REQUEST", "HTTP_RESPONSE"
];

/**
 * Recommended taxonomy for consistency
 */
export const RECOMMENDED_TAXONOMY = {
  serviceNamespace: ["filin.ide", "filin.agent", "filin.server", "filin.chat"],
  component: ["completion", "chat", "lsp-client", "lsp-server", "http-client", "http-server", "chat-handler", "parallel-processor"],
  slowKind: ["autocomplete", "chat", "http", "lsp", "server"],
  hangKind: ["ui_stall", "io_wait", "lock_contention", "external_dependency", "gc_pause", "deadlock_suspected", "unknown"]
};

/**
 * New JSON Lines TraceEvent interface (W3C Trace Context + OpenTelemetry compatible)
 * Uses canonical field names with dot notation through CanonicalFields constants
 */
export interface TraceEvent {
  // Temporal fields
  [CanonicalFields.TIMESTAMP]: string;                    // UTC ISO-8601
  [CanonicalFields.TIMESTAMP_UNIX_NANOS]: string;         // String for precision in JS

  // Identifiers
  [CanonicalFields.EVENT_ID]: string;            // UUIDv7
  [CanonicalFields.TRACE_ID]: string;            // 32 hex characters
  [CanonicalFields.SPAN_ID]: string;             // 16 hex characters
  [CanonicalFields.PARENT_SPAN_ID]?: string;     // 16 hex characters (required for all except root SPAN_START)

  // W3C Trace Context
  [CanonicalFields.TRACE_FLAGS]?: string;
  [CanonicalFields.TRACE_STATE]?: string;

  // Metadata
  [CanonicalFields.LEVEL]: LogLevel;
  [CanonicalFields.EVENT]: TraceEventType;
  [CanonicalFields.EVENT_VERSION]: string;       // "1.0"
  [CanonicalFields.SCHEMA_VERSION]: typeof FILIN_SCHEMA_ID;

  // Resource attributes (OpenTelemetry)
  [CanonicalFields.SERVICE_NAME]: string;
  [CanonicalFields.SERVICE_VERSION]: string;
  [CanonicalFields.SERVICE_INSTANCE_ID]: string;
  [CanonicalFields.SERVICE_NAMESPACE]: string;
  [CanonicalFields.DEPLOYMENT_ENVIRONMENT]: string;
  [CanonicalFields.COMPONENT]: string;
  [CanonicalFields.OPERATION]?: string;
  [CanonicalFields.DIRECTION]?: "sent" | "received";

  // Semantic fields (by event type)
  [CanonicalFields.DURATION_MS]?: number;
  [CanonicalFields.STOP_REASON]?: StopReason;
  [CanonicalFields.DATA_CLASSIFICATION]?: "public" | "internal" | "confidential" | "secret";

  // HTTP semantics (OpenTelemetry stable)
  [CanonicalFields.HTTP_REQUEST_METHOD]?: string;
  [CanonicalFields.URL_PATH]?: string;
  [CanonicalFields.URL_QUERY]?: string;
  [CanonicalFields.URL_FULL]?: string;
  [CanonicalFields.HTTP_RESPONSE_STATUS_CODE]?: number;
  [CanonicalFields.HTTP_ROUTE]?: string;
  [CanonicalFields.USER_AGENT_ORIGINAL]?: string;

  // Network attributes (optional, for diagnostics)
  [CanonicalFields.SERVER_ADDRESS]?: string;
  [CanonicalFields.SERVER_PORT]?: number;
  [CanonicalFields.CLIENT_ADDRESS]?: string;
  [CanonicalFields.NETWORK_PEER_ADDRESS]?: string;
  [CanonicalFields.NETWORK_PEER_PORT]?: number;

  // LSP semantics (JSON-RPC)
  [CanonicalFields.RPC_SYSTEM]?: "jsonrpc";
  [CanonicalFields.RPC_METHOD]?: string;
  [CanonicalFields.RPC_JSONRPC_REQUEST_ID]?: string;
  [CanonicalFields.RPC_JSONRPC_PARAMS_BYTES]?: number;
  [CanonicalFields.RPC_JSONRPC_RESULT_BYTES]?: number;

  // GenAI semantics
  [CanonicalFields.GEN_AI_PROVIDER_NAME]?: string;
  [CanonicalFields.GEN_AI_REQUEST_MODEL]?: string;
  [CanonicalFields.GEN_AI_USAGE_INPUT_TOKENS]?: number;
  [CanonicalFields.GEN_AI_USAGE_OUTPUT_TOKENS]?: number;

  // Runtime Environment (OpenTelemetry Resource)
  [CanonicalFields.RUNTIME_NAME]?: string;
  [CanonicalFields.RUNTIME_VERSION]?: string;
  [CanonicalFields.RUNTIME_DESCRIPTION]?: string;

  // Document and Position (LSP/IDE semantics)
  [CanonicalFields.DOCUMENT_LANGUAGE_ID]?: string;
  [CanonicalFields.DOCUMENT_FILE_NAME]?: string;
  [CanonicalFields.DOCUMENT_POSITION_LINE]?: number;
  [CanonicalFields.DOCUMENT_POSITION_CHARACTER]?: number;
  [CanonicalFields.DOCUMENT_LENGTH]?: number;

  // Completion Metrics
  [CanonicalFields.COMPLETION_COUNT]?: number;
  [CanonicalFields.COMPLETION_HAS_RESULTS]?: boolean;

  // Chat Metrics
  [CanonicalFields.CHAT_MESSAGE_LENGTH]?: number;
  [CanonicalFields.CHAT_MESSAGE_HASH]?: string;
  [CanonicalFields.CHAT_RESPONSE_LENGTH]?: number;
  [CanonicalFields.CHAT_RESPONSE_HASH]?: string;

  // Investigation and correlation
  [CanonicalFields.SESSION_ID]?: string;
  [CanonicalFields.IDE_NAME]?: string;
  [CanonicalFields.IDE_VERSION]?: string;
  [CanonicalFields.PLUGIN_VERSION]?: string;
  [CanonicalFields.OS]?: string;
  [CanonicalFields.WORKSPACE_ID]?: string;
  [CanonicalFields.REPO_HASH]?: string;
  [CanonicalFields.USER_ANONYMOUS_ID]?: string;
  [CanonicalFields.IDE_SESSION_SEQ]?: number;
  [CanonicalFields.REQUEST_IDEMPOTENCY_KEY]?: string;

  // OpenTelemetry links
  [CanonicalFields.LINKS]?: Array<{
    [CanonicalFields.TRACE_ID]: string;
    [CanonicalFields.SPAN_ID]: string;
    attributes?: Record<string, string | number | boolean>;
  }>;

  // Errors
  [CanonicalFields.ERROR_TYPE]?: string;
  [CanonicalFields.ERROR_MESSAGE]?: string;
  [CanonicalFields.ERROR_STACK]?: string;
  [CanonicalFields.ERROR_STACK_TRUNCATED]?: boolean;
  [CanonicalFields.CAUSE_CHAIN]?: Array<{
    [CanonicalFields.ERROR_TYPE]: string;
    [CanonicalFields.ERROR_MESSAGE]?: string;
  }>;

  // Streaming
  [CanonicalFields.STREAM_ID]?: string;
  [CanonicalFields.STREAM_SEQ]?: number;
  [CanonicalFields.STREAM_CHUNKS_TOTAL]?: number;
  [CanonicalFields.BYTES_TOTAL]?: number;
  [CanonicalFields.TIME_TO_FIRST_TOKEN]?: number;
  [CanonicalFields.TIME_TO_LAST_BYTE]?: number;
  [CanonicalFields.CHUNK_INDEX]?: number;

  // Diagnostics
  [CanonicalFields.SLOW_KIND]?: string;
  [CanonicalFields.HANG_KIND]?: string;

  // Custom attributes (canonical naming only)
  [key: `attributes.${string}`]: string | number | boolean;
}

/**
 * Интерфейс логгера (общий для всех компонентов)
 */
export interface Logger {
  error(msg: string, error?: any): void;
  warn(msg: string): void;
  info(msg: string): void;
  debug?(msg: string): void;
  trace?(msg: string, verbose?: any): void;
}

/**
 * Интерфейс конфигурации безопасности
 */
export interface SecurityConfig {
  maskTokens: boolean;
  maskPatterns: string[];
}

/**
 * Интерфейс конфигурации таймаутов
 */
export interface TimeoutsConfig {
  operationHangMs: number;
  httpTimeoutMs: number;
  lspTimeoutMs: number;
  slowOperationMs: number;
}

/**
 * Интерфейс конфигурации трассировки
 */
export interface TraceConfig {
  enabled: boolean;
  level: string;
  slowOperationMs: number;
  httpTimeoutMs: number;
  lspTimeoutMs: number;
  operationHangMs: number;
  maskTokens: boolean;
  maskPatterns: string[];
  security?: SecurityConfig;
  timeouts?: TimeoutsConfig;

  // Новые поля для модернизации
  autocompleteSlowMs: number;
  chatSlowMs: number;
  testMode: boolean;
  streamFullLog: boolean;

  // Security and data classification
  dataClassification?: string;

  // OTLP конфигурация с поддержкой Jaeger
  otlp: {
    endpoint: string;
    tracesEndpoint?: string;
    protocol?: string;
    headers: Record<string, string>;
  };

  // Jaeger конфигурация
  jaeger?: {
    uiPort: number;
    uiUrl: string;
    otlpGrpcPort: number;
    otlpHttpPort: number;
    samplingPort: number;
    zipkinPort: number;
  };

  // OpenTelemetry Service конфигурация
  otelService?: {
    name: string;
    version: string;
    environment: string;
  };

  // Sampling
  sampling: {
    ratio: number;
    forceSampleOnError: boolean;
  };

  // Streaming
  streaming: {
    logEveryNChunks: number;
    maxChunksLogged: number;
  };
}

/**
 * Константы для трассировки
 */
export const TRACE_CONSTANTS = {
  PREFIX: '[FilinTracer]',
  TRACE_ID_LENGTH: 32,
  TRACE_ID_PATTERN: /^[a-f0-9]{32}$/,
  ENV_VAR_NAME: 'FILIN_TRACE_ENABLED',
  CONFIG_FILE_NAME: 'config.yaml'
} as const;